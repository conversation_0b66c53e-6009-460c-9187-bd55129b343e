import axios from 'axios';

const API_URL = import.meta?.env?.VITE_BACKEND_URL || 'http://localhost:3010';

// Create an axios instance for CRUD operations
const apiClient = axios.create({
  baseURL: API_URL,
  timeout: 10000, // 10 seconds timeout
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Add response interceptor for better error handling
apiClient.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export default {
  // Projects
  getProjects() {
    console.log('Fetching projects...');
    
    // Get company data from localStorage
    const companyDataStr = localStorage.getItem('companyData');
    const companyData = companyDataStr ? JSON.parse(companyDataStr) : null;
    const companyId = companyData?.id;
    
    // Add companyId to params if available
    const params = companyId ? { companyId } : {};
    console.log('Using params:', params);
    
    return apiClient.get('/projects', { params });
  },
  
  getProject(id) {
    return apiClient.get(`/projects/${id}`);
  },
  
  createProject(project) {
    // Get company data from localStorage
    const companyDataStr = localStorage.getItem('companyData');
    const companyData = companyDataStr ? JSON.parse(companyDataStr) : null;
    const companyId = companyData?.id;
    
    // Add companyId to params if available
    const params = companyId ? { companyId } : {};
    
    return apiClient.post('/projects', project, { params });
  },
  
  updateProject(id, project) {
    return apiClient.patch(`/projects/${id}`, project);
  },
  
  deleteProject(id) {
    return apiClient.delete(`/projects/${id}`);
  }
};
