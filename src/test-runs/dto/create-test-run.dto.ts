import { IsNotEmpty, IsString, <PERSON><PERSON>ptional, IsDateString, Is<PERSON><PERSON>y, IsUUID, IsE<PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TestCaseSelectionType } from '../test-run.entity';

export class DynamicFiltersDto {
  @IsArray()
  @IsOptional()
  priorities?: string[];

  @IsArray()
  @IsOptional()
  platforms?: string[];

  @IsArray()
  @IsOptional()
  testCaseTypes?: string[];

  @IsArray()
  @IsOptional()
  folders?: string[];

  @IsArray()
  @IsOptional()
  tags?: string[];
}

export class CreateTestRunDto {
  @ApiProperty({
    description: 'Test run name',
    example: 'Sprint 1 Regression'
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'Test run description',
    example: 'Regression test suite for Sprint 1 features'
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Test case selection type',
    enum: TestCaseSelectionType,
    example: TestCaseSelectionType.ALL
  })
  @IsEnum(TestCaseSelectionType)
  @IsNotEmpty()
  selectionType: TestCaseSelectionType;

  @ApiPropertyOptional({
    description: 'Test run start time',
    example: '2024-03-17T10:00:00Z'
  })
  @IsDateString()
  @IsOptional()
  startTime?: Date;

  @ApiPropertyOptional({
    description: 'Test environment',
    example: 'staging'
  })
  @IsString()
  @IsOptional()
  environment?: string;

  @ApiPropertyOptional({
    description: 'Build version',
    example: 'v1.2.3'
  })
  @IsString()
  @IsOptional()
  build?: string;

  @ApiPropertyOptional({
    description: 'Release version',
    example: 'R2024.1'
  })
  @IsString()
  @IsOptional()
  release?: string;

  @ApiProperty({
    description: 'Array of test case IDs to include in the test run',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************']
  })
  @IsArray()
  @IsUUID('4', { each: true })
  testCaseIds: string[];

  @IsOptional()
  dynamicFilters?: {
    priorities?: string[];
    platforms?: string[];
    testCaseTypes?: string[];
    folders?: string[];
    tags?: string[];
  };

  @ApiPropertyOptional({
    description: 'Selected test case IDs',
    example: '["id1", "id2"]'
  })
  @IsOptional()
  @IsArray()
  selectedTestCaseIds?: string[];
}