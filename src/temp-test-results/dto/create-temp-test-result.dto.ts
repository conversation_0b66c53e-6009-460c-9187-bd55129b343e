import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsUUID, IsEnum, IsOptional, IsArray, IsObject, IsDateString, IsNumber } from 'class-validator';
import { TempTestResultStatus } from '../temp-test-result.entity';

export class CreateTempTestResultDto {
  @ApiProperty({
    description: 'Test case ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  testCaseId: string;

  @ApiProperty({
    description: 'Test case sequential ID',
    example: 'TC-001'
  })
  @IsString()
  tcId: string;

  @ApiProperty({
    description: 'Project ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  projectId: string;

  @ApiProperty({
    description: 'Test execution status',
    enum: TempTestResultStatus,
    example: TempTestResultStatus.PASSED
  })
  @IsEnum(TempTestResultStatus)
  status: TempTestResultStatus;

  @ApiPropertyOptional({
    description: 'Test execution summary',
    example: 'Test completed successfully with 5 steps'
  })
  @IsOptional()
  @IsString()
  summary?: string;

  @ApiPropertyOptional({
    description: 'Test execution duration in milliseconds',
    example: 15000
  })
  @IsOptional()
  @IsNumber()
  duration?: number;

  @ApiPropertyOptional({
    description: 'Test execution logs',
    example: ['🔑 Fetching AgentQ API key...', '🔗 Connecting to WebSocket server...']
  })
  @IsOptional()
  @IsArray()
  logs?: string[];

  @ApiPropertyOptional({
    description: 'Google Cloud Storage URL for logs',
    example: 'gs://agentq-test-logs/test-123/logs.json'
  })
  @IsString()
  @IsOptional()
  logsUrl?: string;

  @ApiPropertyOptional({
    description: 'Google Cloud Storage URL for test execution video',
    example: 'gs://agentq-test-logs/test-123/video.webm'
  })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  @ApiPropertyOptional({
    description: 'Test data used for execution',
    example: '{"steps": [...], "testCase": {...}}'
  })
  @IsOptional()
  @IsObject()
  testData?: any;

  @ApiPropertyOptional({
    description: 'Error message if test failed',
    example: 'Element not found: #login-button'
  })
  @IsOptional()
  @IsString()
  errorMessage?: string;

  @ApiPropertyOptional({
    description: 'When the test was executed',
    example: '2024-02-20T12:00:00Z'
  })
  @IsOptional()
  @IsDateString()
  executedAt?: string;
}
