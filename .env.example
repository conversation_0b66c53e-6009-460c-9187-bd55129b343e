# Backend Configuration
BACKEND_URL=http://localhost:3010

# Frontend Environment Variables
VITE_BACKEND_URL=http://localhost:3010

# WebSocket URL for automation testing
VITE_WEBSOCKET_URL=ws://localhost:3008
VITE_WEBSOCKET_URL2=ws://localhost:3009

# AI Service URL
VITE_AI_SERVICE_URL=http://localhost:3011

# AgentQ API Configuration
AGENTQ_API_KEY=your_agentq_api_key_here
VITE_AGENTQ_API_KEY=your_agentq_api_key_here

# Redis Configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# BullMQ Dashboard
DASHBOARD_PORT=3012

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_change_in_production

# Google Cloud Storage (if using)
GCP_PROJECT_ID=your_gcp_project_id
GCP_CLIENT_EMAIL=your_gcp_client_email
GCP_PRIVATE_KEY=your_gcp_private_key
GCP_BUCKET_NAME=your_gcp_bucket_name


