<template>
  <div v-if="modelValue" class="modal-overlay">
    <div class="filter-modal">
      <div class="modal-header">
        <h3>Filter Test Results</h3>
        <button class="close-button" @click="closeModal">×</button>
      </div>

      <div class="modal-body">
        <div class="filter-grid">
          <!-- Status Filter -->
          <div class="filter-group">
            <h4>Status</h4>
            <div class="checkbox-group">
              <div
                v-for="status in statusOptions"
                :key="status"
                class="checkbox-item"
              >
                <label>
                  <input
                    type="checkbox"
                    :checked="filters.status.includes(status)"
                    @change="toggleStatusFilter(status)"
                  />
                  <span
                    class="status-badge small"
                    :class="status"
                  >
                    {{ status }}
                  </span>
                </label>
              </div>
            </div>
          </div>

          <!-- Priority Filter -->
          <div class="filter-group">
            <h4>Priority</h4>
            <div class="checkbox-group">
              <div
                v-for="priority in priorityOptions"
                :key="priority"
                class="checkbox-item"
              >
                <label>
                  <input
                    type="checkbox"
                    :checked="filters.priority.includes(priority)"
                    @change="togglePriorityFilter(priority)"
                  />
                  {{ priority }}
                </label>
              </div>
            </div>
          </div>

          <!-- Tags Filter -->
          <div class="filter-group">
            <h4>Tags</h4>
            <div class="checkbox-group">
              <div
                v-for="tag in tagOptions"
                :key="tag.id"
                class="checkbox-item"
              >
                <label>
                  <input
                    type="checkbox"
                    :checked="filters.tagIds.includes(tag.id)"
                    @change="toggleTagFilter(tag.id)"
                  />
                  {{ tag.name }}
                </label>
              </div>
            </div>
          </div>

          <!-- Type Filter -->
          <div class="filter-group">
            <h4>Test Type</h4>
            <div class="checkbox-group">
              <div
                v-for="type in typeOptions"
                :key="type"
                class="checkbox-item"
              >
                <label>
                  <input
                    type="checkbox"
                    :checked="filters.type.includes(type)"
                    @change="toggleTypeFilter(type)"
                  />
                  {{ type }}
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button @click="resetFilters" class="reset-button">Reset</button>
        <button @click="closeModal" class="cancel-button">Cancel</button>
        <button @click="applyFilters" class="apply-button">Apply Filters</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface Tag {
  id: string;
  name: string;
}

const props = defineProps<{
  modelValue: boolean;
  statusOptions: string[];
  priorityOptions: string[];
  tagOptions: Tag[];
  typeOptions: string[];
  initialFilters: {
    status: string[];
    priority: string[];
    tagIds: string[];
    type: string[];
  };
}>();

type Filters = {
  status: string[];
  priority: string[];
  tagIds: string[];
  type: string[];
};

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'apply', filters: Filters): void;
}>();

// Local copy of filters for the modal
const filters = ref({
  status: [...props.initialFilters.status],
  priority: [...props.initialFilters.priority],
  tagIds: [...props.initialFilters.tagIds],
  type: [...props.initialFilters.type]
});

// Watch for changes in initialFilters
watch(() => props.initialFilters, (newFilters) => {
  if (props.modelValue) return; // Don't update while modal is open

  filters.value = {
    status: [...newFilters.status],
    priority: [...newFilters.priority],
    tagIds: [...newFilters.tagIds],
    type: [...newFilters.type]
  };
}, { deep: true });

// Close modal
const closeModal = () => {
  emit('update:modelValue', false);
};

// Apply filters
const applyFilters = () => {
  emit('apply', {
    status: [...filters.value.status],
    priority: [...filters.value.priority],
    tagIds: [...filters.value.tagIds],
    type: [...filters.value.type]
  });
  closeModal();
};

// Reset filters
const resetFilters = () => {
  filters.value = {
    status: [],
    priority: [],
    tagIds: [],
    type: []
  };
};

// Toggle filters
const toggleStatusFilter = (status: string) => {
  const index = filters.value.status.indexOf(status);
  if (index === -1) {
    filters.value.status.push(status);
  } else {
    filters.value.status.splice(index, 1);
  }
};

const togglePriorityFilter = (priority: string) => {
  const index = filters.value.priority.indexOf(priority);
  if (index === -1) {
    filters.value.priority.push(priority);
  } else {
    filters.value.priority.splice(index, 1);
  }
};

const toggleTagFilter = (tagId: string) => {
  const index = filters.value.tagIds.indexOf(tagId);
  if (index === -1) {
    filters.value.tagIds.push(tagId);
  } else {
    filters.value.tagIds.splice(index, 1);
  }
};

const toggleTypeFilter = (type: string) => {
  const index = filters.value.type.indexOf(type);
  if (index === -1) {
    filters.value.type.push(type);
  } else {
    filters.value.type.splice(index, 1);
  }
};
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.filter-modal {
  background-color: white;
  border-radius: 8px;
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #374151;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;

    &:hover {
      color: #374151;
    }
  }
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 130px);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.filter-group {
  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #4b5563;
  }

  .checkbox-group {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 8px;
    background-color: white;

    .checkbox-item {
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        cursor: pointer;

        input[type="checkbox"] {
          cursor: pointer;
        }
      }
    }
  }
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
  }

  .reset-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .apply-button {
    background-color: #10b981;
    color: white;
    border: none;

    &:hover {
      background-color: #059669;
    }
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;

  &.small {
    padding: 2px 6px;
    font-size: 11px;
  }

  &.passed {
    background-color: #dcfce7;
    color: #15803d;
  }

  &.failed {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  &.blocked {
    background-color: #fff7ed;
    color: #9a3412;
  }

  &.skipped {
    background-color: #eff6ff;
    color: #1d4ed8;
  }

  &.untested {
    background-color: #f4f4f5;
    color: #3f3f46;
  }
}
</style>
