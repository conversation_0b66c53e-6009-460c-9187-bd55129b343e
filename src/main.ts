import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import axios from 'axios';
import { useAuthStore } from './stores/auth';

import './assets/styles/main.scss';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(router);

// Configure axios interceptors for automatic token refresh
axios.interceptors.request.use(async (config) => {
  // Get the auth store
  const authStore = useAuthStore();
  
  // Check if token needs refreshing before making the request
  if (authStore.isAuthenticated) {
    await authStore.checkTokenExpiry();
    
    // Update the Authorization header with the current token
    config.headers.Authorization = authStore.getAuthHeader;
  }
  
  return config;
}, (error) => {
  return Promise.reject(error);
});

axios.interceptors.response.use((response) => {
  return response;
}, async (error) => {
  const originalRequest = error.config;
  
  // If the error is 401 and we haven't already tried to refresh the token
  if (error.response?.status === 401 && !originalRequest._retry) {
    originalRequest._retry = true;
    
    const authStore = useAuthStore();
    console.log('Attempting to refresh token due to 401 error');
    
    // Try to refresh the token
    const refreshed = await authStore.refreshAuthToken();
    
    if (refreshed) {
      console.log('Token refreshed successfully, retrying request');
      // Update the authorization header
      originalRequest.headers.Authorization = authStore.getAuthHeader;
      
      // Retry the original request
      return axios(originalRequest);
    } else {
      console.log('Token refresh failed, redirecting to login');
      // If refresh failed, redirect to login
      authStore.logout();
      router.push('/login');
      return Promise.reject(error);
    }
  }
  
  return Promise.reject(error);
});

app.mount('#app');
