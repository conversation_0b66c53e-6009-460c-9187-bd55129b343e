import { <PERSON>ti<PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, UpdateDateColumn, ManyToMany, JoinTable } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Project } from '../projects/project.entity';
import { TestCaseFolder } from './folder.entity';
import { Tag } from './tag.entity';

export enum TestCaseType {
  MANUAL = 'manual',
  AUTOMATION = 'automation'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum Platform {
  WEB = 'web',
  MOBILE = 'mobile',
  API = 'api',
  DESKTOP = 'desktop'
}

@Entity('test_cases')
export class TestCase {
  @ApiProperty({
    description: 'The unique identifier of the test case',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'The sequential ID of the test case within the project',
    example: 1
  })
  @Column()
  tcId: number;

  @ApiProperty({
    description: 'Test case title',
    example: 'User Login Validation'
  })
  @Column()
  title: string;

  @ApiProperty({
    description: 'Test case precondition',
    example: 'User is not logged in'
  })
  @Column({ type: 'text', nullable: true })
  precondition: string;

  @ApiProperty({
    description: 'Test case steps',
    example: '1. Navigate to login page\n2. Enter credentials\n3. Click login button'
  })
  @Column({ type: 'text' })
  steps: string;

  @ApiProperty({
    description: 'Expected results',
    example: 'User should be redirected to dashboard'
  })
  @Column({ type: 'text' })
  expectation: string;

  @ApiProperty({
    description: 'Test case priority',
    enum: Priority,
    example: Priority.HIGH
  })
  @Column({
    type: 'enum',
    enum: Priority,
    default: Priority.MEDIUM
  })
  priority: Priority;

  @ApiProperty({
    description: 'Test case type',
    example: 'functional'
  })
  @Column()
  type: string;

  @ApiProperty({
    description: 'Platform',
    enum: Platform,
    example: Platform.WEB
  })
  @Column({
    type: 'enum',
    enum: Platform,
    default: Platform.WEB
  })
  platform: Platform;

  @ApiProperty({
    description: 'Test case automation status',
    enum: TestCaseType,
    example: TestCaseType.MANUAL
  })
  @Column({
    type: 'enum',
    enum: TestCaseType,
    default: TestCaseType.MANUAL
  })
  testCaseType: TestCaseType;

  @ManyToOne(() => Project, { onDelete: 'CASCADE' })
  project: Project;

  @Column()
  projectId: string;

  @ManyToOne(() => TestCaseFolder, { onDelete: 'SET NULL', nullable: true })
  folder: TestCaseFolder;

  @Column({ nullable: true })
  folderId: string;

  @ManyToMany(() => Tag, tag => tag.testCases)
  @JoinTable({
    name: 'testcase_tags',
    joinColumn: {
      name: 'testcaseId',
      referencedColumnName: 'id'
    },
    inverseJoinColumn: {
      name: 'tagId',
      referencedColumnName: 'id'
    }
  })
  tags: Tag[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ApiProperty({
    description: 'Whether the test case is automated by AgentQ',
    example: false
  })
  @Column({ default: false })
  automationByAgentq: boolean;
}
