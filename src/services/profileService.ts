import axios from 'axios';
import { useAuthStore } from '../stores/auth';

const API_URL = `${(import.meta as any).env.VITE_CORE_SERVICE_URL}`;

export const profileService = {
  async getProfile() {
    const authStore = useAuthStore();
    
    try {
      const response = await axios.get(`${API_URL}/profile`, {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw error;
    }
  },
  
  async getUsage() {
    const authStore = useAuthStore();
    
    try {
      const response = await axios.get(`${API_URL}/profile/usage`, {
        headers: {
          'Authorization': authStore.getAuthHeader
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Error fetching usage data:', error);
      throw error;
    }
  }
};