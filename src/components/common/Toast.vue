<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';

interface ToastMessage {
  id: number;
  message: string;
  type: 'success' | 'error' | 'info' | 'warning';
  duration: number;
}

const toasts = ref<ToastMessage[]>([]);
let nextId = 1;

const addToast = (message: string, type: 'success' | 'error' | 'info' | 'warning' = 'info', duration = 5000) => {
  const id = nextId++;
  const toast = { id, message, type, duration };
  toasts.value.push(toast);
  
  // Auto-remove after duration
  setTimeout(() => {
    removeToast(id);
  }, duration);
  
  return id;
};

const removeToast = (id: number) => {
  const index = toasts.value.findIndex(t => t.id === id);
  if (index !== -1) {
    toasts.value.splice(index, 1);
  }
};

// Listen for custom toast events
const handleShowToast = (event: CustomEvent) => {
  const { message, type, duration } = event.detail;
  addToast(message, type, duration);
};

onMounted(() => {
  window.addEventListener('show-toast', handleShowToast as EventListener);
});

onUnmounted(() => {
  window.removeEventListener('show-toast', handleShowToast as EventListener);
});
</script>

<template>
  <div class="toast-container">
    <transition-group name="toast">
      <div 
        v-for="toast in toasts" 
        :key="toast.id" 
        :class="['toast', `toast-${toast.type}`]"
        @click="removeToast(toast.id)"
      >
        <div class="toast-content">
          <div class="toast-icon">
            <span v-if="toast.type === 'success'">✓</span>
            <span v-else-if="toast.type === 'error'">✗</span>
            <span v-else-if="toast.type === 'warning'">⚠</span>
            <span v-else>ℹ</span>
          </div>
          <div class="toast-message">{{ toast.message }}</div>
        </div>
        <button class="toast-close" @click.stop="removeToast(toast.id)">×</button>
      </div>
    </transition-group>
  </div>
</template>

<style lang="scss" scoped>
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}

.toast {
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  
  &-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }
  
  &-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-weight: bold;
  }
  
  &-message {
    font-size: 14px;
    line-height: 1.4;
  }
  
  &-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    opacity: 0.6;
    
    &:hover {
      opacity: 1;
    }
  }
  
  &-success {
    background-color: #ecfdf5;
    border-left: 4px solid #10b981;
    color: #065f46;
    
    .toast-icon {
      background-color: #10b981;
      color: white;
    }
    
    .toast-close {
      color: #065f46;
    }
  }
  
  &-error {
    background-color: #fef2f2;
    border-left: 4px solid #ef4444;
    color: #b91c1c;
    
    .toast-icon {
      background-color: #ef4444;
      color: white;
    }
    
    .toast-close {
      color: #b91c1c;
    }
  }
  
  &-warning {
    background-color: #fffbeb;
    border-left: 4px solid #f59e0b;
    color: #92400e;
    
    .toast-icon {
      background-color: #f59e0b;
      color: white;
    }
    
    .toast-close {
      color: #92400e;
    }
  }
  
  &-info {
    background-color: #eff6ff;
    border-left: 4px solid #3b82f6;
    color: #1e40af;
    
    .toast-icon {
      background-color: #3b82f6;
      color: white;
    }
    
    .toast-close {
      color: #1e40af;
    }
  }
}

// Transition animations
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
