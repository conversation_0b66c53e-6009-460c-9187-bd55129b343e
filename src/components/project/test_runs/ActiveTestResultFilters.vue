<template>
  <div v-if="hasActiveFilters" class="active-filters">
    <div class="active-filters-header">
      <h4>Active Filters:</h4>
      <button @click="clearAll" class="clear-all-button">Clear All</button>
    </div>
    
    <div class="filter-tags">
      <div v-for="(status, index) in filters.status" :key="`status-${index}`" class="filter-tag">
        <span 
          class="status-badge small" 
          :class="status"
        >
          {{ status }}
        </span>
      </div>
      
      <div v-for="(priority, index) in filters.priority" :key="`priority-${index}`" class="filter-tag">
        Priority: {{ priority }}
      </div>
      
      <div v-for="tagId in filters.tagIds" :key="`tag-${tagId}`" class="filter-tag">
        {{ getTagName(tagId) }}
      </div>
      
      <div v-for="(type, index) in filters.type" :key="`type-${index}`" class="filter-tag">
        Type: {{ type }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Tag {
  id: string;
  name: string;
}

const props = defineProps<{
  filters: {
    status: string[];
    priority: string[];
    tagIds: string[];
    type: string[];
  };
  tagOptions: Tag[];
}>();

const emit = defineEmits<{
  (e: 'clear'): void;
}>();

// Check if there are any active filters
const hasActiveFilters = computed(() => {
  return (
    props.filters.status.length > 0 ||
    props.filters.priority.length > 0 ||
    props.filters.tagIds.length > 0 ||
    props.filters.type.length > 0
  );
});

// Get tag name by ID
const getTagName = (id: string): string => {
  const tag = props.tagOptions.find(t => t.id === id);
  return tag?.name || 'Unknown Tag';
};

// Clear all filters
const clearAll = () => {
  emit('clear');
};
</script>

<style lang="scss" scoped>
.active-filters {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  
  .active-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    h4 {
      margin: 0;
      font-size: 14px;
      color: #4b5563;
    }
    
    .clear-all-button {
      padding: 4px 8px;
      background-color: transparent;
      color: #10b981;
      border: 1px solid #10b981;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      
      &:hover {
        background-color: #d1fae5;
      }
    }
  }
  
  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .filter-tag {
      background-color: white;
      border: 1px solid #e5e7eb;
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 12px;
      color: #4b5563;
      display: flex;
      align-items: center;
    }
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  margin: 0;

  &.small {
    padding: 2px 6px;
    font-size: 11px;
  }

  &.passed {
    background-color: #dcfce7;
    color: #15803d;
  }

  &.failed {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  &.blocked {
    background-color: #fff7ed;
    color: #9a3412;
  }

  &.skipped {
    background-color: #eff6ff;
    color: #1d4ed8;
  }

  &.untested {
    background-color: #f4f4f5;
    color: #3f3f46;
  }
}
</style>
