<template>
  <div class="external-auth-container">
    <div v-if="loading" class="loading-spinner"></div>
    <p v-if="loading">Authenticating, please wait...</p>
    <div v-if="error" class="error-message">
      <p>{{ error }}</p>
      <p>Redirecting to login page...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import axios from 'axios';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const error = ref('');
const loading = ref(true);

onMounted(async () => {
  try {
    // Get token from URL
    const token = route.query.token as string;
    const companyData = route.query.company as string;
    
    if (!token) {
      throw new Error('No authentication token provided');
    }
    
    // Save token to auth store
    await authStore.setExternalToken(token);
    
    // Process company data if available
    if (companyData) {
      try {
        const company = JSON.parse(decodeURIComponent(companyData));
        localStorage.setItem('companyData', JSON.stringify(company));
        
        // Register company in the backend
        if (company.id && company.name) {
          await registerCompanyInBackend(token, company);
        }
      } catch (e) {
        console.error('Failed to process company data', e);
      }
    }
    
    loading.value = false;
    // Redirect to projects page
    router.push('/projects');
  } catch (err) {
    console.error('Authentication error:', err);
    error.value = err instanceof Error ? err.message : 'Authentication failed';
    loading.value = false;
    setTimeout(() => {
      router.push('/login');
    }, 3000);
  }
});

// Function to register company in the backend
async function registerCompanyInBackend(token: string, company: any) {
  try {
    const apiUrl = `${(import.meta as any).env.VITE_BACKEND_URL}/companies/register`;
    await axios.post(apiUrl, {
      id: company.id,
      name: company.name,
      userId: company.user?.id,
      subscription: company.subscription
    }, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    console.log('Company registered successfully');
  } catch (error) {
    console.error('Failed to register company:', error);
  }
}
</script>

<style scoped>
.external-auth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
  margin-bottom: 20px;
}

.error-message {
  color: #e74c3c;
  text-align: center;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
