{
  // "extends": "./tsconfig.json",

    "compilerOptions": {
      "target": "es2020",
      "useDefineForClassFields": true,
      "module": "es2020",
      "lib": ["es2020", "DOM", "DOM.Iterable"],
      "skipLibCheck": true,

      /* Bundler mode */
      "moduleResolution": "bundler",
      "allowImportingTsExtensions": true,
      "isolatedModules": true,
      "moduleDetection": "force",
      "noEmit": true,
      "jsx": "preserve",

      /* Path mapping */
      "baseUrl": ".",
      "paths": {
        "@/*": ["src/*"]
      },
  
      /* Linting */
      "strict": true,
      "noUnusedLocals": true,
      "noUnusedParameters": true,
      "noFallthroughCasesInSwitch": true
    },
  "include": ["vite.config.ts", "src/*.ts", "src/*.vue", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*/*.ts", "src/**/*/*.d.ts", "src/**/*/*.tsx", "src/**/*/*.vue", "src/**/*/*/*.vue"]
}