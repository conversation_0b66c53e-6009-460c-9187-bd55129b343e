import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Project } from './project.entity';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectRepository(Project)
    private projectsRepository: Repository<Project>,
  ) {}

  async findAll(userId: string, companyId?: string): Promise<Project[]> {
    console.log(`Finding projects for user ${userId} and company ${companyId || 'none'}`);
    
    if (companyId) {
      return this.projectsRepository.find({
        where: { companyId },
        order: { createdAt: 'DESC' }
      });
    } else {
      return this.projectsRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' }
      });
    }
  }

  async create(createProjectDto: CreateProjectDto, userId: string, companyId?: string): Promise<Project> {
    const project = this.projectsRepository.create({
      ...createProjectDto,
      userId,
      companyId
    });
    
    return this.projectsRepository.save(project);
  }

  async findOne(id: string, companyId: string): Promise<Project> {
    const project = await this.projectsRepository.findOne({
      where: { id, companyId }
    });
    
    if (!project) {
      throw new NotFoundException(`Project with ID ${id} not found`);
    }
    
    return project;
  }

  async update(id: string, userId: string, updateProjectDto: UpdateProjectDto): Promise<Project> {
    const project = await this.findOne(id, userId);
    
    Object.assign(project, updateProjectDto);
    
    return this.projectsRepository.save(project);
  }

  async remove(id: string, userId: string): Promise<void> {
    const project = await this.findOne(id, userId);
    
    await this.projectsRepository.remove(project);
  }
}
