import express from 'express';
import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { QueueService } from './queue-service';

export class DashboardService {
  private app: express.Application;
  private serverAdapter: ExpressAdapter;
  private queueService: QueueService;

  constructor() {
    this.app = express();
    this.queueService = QueueService.getInstance();
    this.serverAdapter = new ExpressAdapter();
    this.setupDashboard();
    this.setupRoutes();
  }

  private setupDashboard(): void {
    // Set up the Bull Board dashboard
    this.serverAdapter.setBasePath('/admin/queues');
    
    const { addQueue, removeQueue, setQueues, replaceQueues } = createBullBoard({
      queues: [new BullMQAdapter(this.queueService.getQueue())],
      serverAdapter: this.serverAdapter,
    });

    // Use the dashboard middleware
    this.app.use('/admin/queues', this.serverAdapter.getRouter());
  }

  private setupRoutes(): void {
    // Enable CORS for the dashboard
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
      if (req.method === 'OPTIONS') {
        res.sendStatus(200);
      } else {
        next();
      }
    });

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ status: 'ok', timestamp: new Date().toISOString() });
    });

    // Queue statistics endpoint
    this.app.get('/api/queue/stats', async (req, res) => {
      try {
        const stats = await this.queueService.getQueueStats();
        res.json(stats);
      } catch (error) {
        console.error('Failed to get queue stats:', error);
        res.status(500).json({ error: 'Failed to get queue statistics' });
      }
    });

    // Get job status endpoint
    this.app.get('/api/queue/job/:jobId', async (req, res) => {
      try {
        const { jobId } = req.params;
        const jobStatus = await this.queueService.getJobStatus(jobId);
        
        if (!jobStatus) {
          return res.status(404).json({ error: 'Job not found' });
        }
        
        res.json(jobStatus);
      } catch (error) {
        console.error('Failed to get job status:', error);
        res.status(500).json({ error: 'Failed to get job status' });
      }
    });

    // Cancel job endpoint
    this.app.delete('/api/queue/job/:jobId', async (req, res) => {
      try {
        const { jobId } = req.params;
        const cancelled = await this.queueService.cancelJob(jobId);
        
        if (cancelled) {
          res.json({ message: 'Job cancelled successfully' });
        } else {
          res.status(404).json({ error: 'Job not found or could not be cancelled' });
        }
      } catch (error) {
        console.error('Failed to cancel job:', error);
        res.status(500).json({ error: 'Failed to cancel job' });
      }
    });

    // Dashboard home page
    this.app.get('/', (req, res) => {
      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>AgentQ Test Queue Dashboard</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 40px; 
              background-color: #f5f5f5; 
            }
            .container { 
              max-width: 800px; 
              margin: 0 auto; 
              background: white; 
              padding: 30px; 
              border-radius: 8px; 
              box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            }
            h1 { 
              color: #333; 
              text-align: center; 
              margin-bottom: 30px; 
            }
            .stats { 
              display: grid; 
              grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); 
              gap: 20px; 
              margin: 30px 0; 
            }
            .stat-card { 
              background: #f8f9fa; 
              padding: 20px; 
              border-radius: 6px; 
              text-align: center; 
              border-left: 4px solid #007bff; 
            }
            .stat-number { 
              font-size: 2em; 
              font-weight: bold; 
              color: #007bff; 
            }
            .stat-label { 
              color: #666; 
              margin-top: 5px; 
            }
            .links { 
              text-align: center; 
              margin-top: 30px; 
            }
            .links a { 
              display: inline-block; 
              margin: 10px; 
              padding: 12px 24px; 
              background: #007bff; 
              color: white; 
              text-decoration: none; 
              border-radius: 4px; 
              transition: background 0.3s; 
            }
            .links a:hover { 
              background: #0056b3; 
            }
            .api-endpoints {
              margin-top: 30px;
              padding: 20px;
              background: #f8f9fa;
              border-radius: 6px;
            }
            .api-endpoints h3 {
              margin-top: 0;
              color: #333;
            }
            .endpoint {
              margin: 10px 0;
              font-family: monospace;
              background: white;
              padding: 8px;
              border-radius: 4px;
              border-left: 3px solid #28a745;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>🚀 AgentQ Test Queue Dashboard</h1>
            
            <div class="stats" id="stats">
              <div class="stat-card">
                <div class="stat-number" id="waiting">-</div>
                <div class="stat-label">Waiting</div>
              </div>
              <div class="stat-card">
                <div class="stat-number" id="active">-</div>
                <div class="stat-label">Active</div>
              </div>
              <div class="stat-card">
                <div class="stat-number" id="completed">-</div>
                <div class="stat-label">Completed</div>
              </div>
              <div class="stat-card">
                <div class="stat-number" id="failed">-</div>
                <div class="stat-label">Failed</div>
              </div>
            </div>

            <div class="links">
              <a href="/admin/queues" target="_blank">📊 Queue Management</a>
              <a href="/api/queue/stats" target="_blank">📈 API Stats</a>
            </div>

            <div class="api-endpoints">
              <h3>📡 API Endpoints</h3>
              <div class="endpoint">GET /api/queue/stats - Get queue statistics</div>
              <div class="endpoint">GET /api/queue/job/:jobId - Get job status</div>
              <div class="endpoint">DELETE /api/queue/job/:jobId - Cancel job</div>
              <div class="endpoint">GET /health - Health check</div>
            </div>
          </div>

          <script>
            async function updateStats() {
              try {
                const response = await fetch('/api/queue/stats');
                const stats = await response.json();
                
                document.getElementById('waiting').textContent = stats.waiting;
                document.getElementById('active').textContent = stats.active;
                document.getElementById('completed').textContent = stats.completed;
                document.getElementById('failed').textContent = stats.failed;
              } catch (error) {
                console.error('Failed to fetch stats:', error);
              }
            }

            // Update stats every 5 seconds
            updateStats();
            setInterval(updateStats, 5000);
          </script>
        </body>
        </html>
      `);
    });
  }

  public start(port: number = 3020): void {
    this.app.listen(port, () => {
      console.log(`🎯 Queue Dashboard running on http://localhost:${port}`);
      console.log(`📊 Queue Management UI: http://localhost:${port}/admin/queues`);
      console.log(`📈 API Stats: http://localhost:${port}/api/queue/stats`);
    });
  }

  public getApp(): express.Application {
    return this.app;
  }
}
