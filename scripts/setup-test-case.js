#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to set up a test case with proper login credentials
 * This script will create or update the "Verify Successful User Login with Valid Credentials" test case
 * with the correct automation steps and valid credentials.
 */

const axios = require('axios');

const BACKEND_URL = process.env.VITE_BACKEND_URL || 'http://localhost:3010';
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';
const LOGIN_URL = 'http://localhost:5173/login'; // Frontend login page

// Test will use JWT_SECRET for API authentication instead of user credentials

async function setupTestCase() {
  try {
    console.log('🚀 Setting up test case with valid credentials...');
    
    // Create a service user for API operations (one-time setup)
    console.log('📝 Setting up service user...');
    const serviceEmail = '<EMAIL>';
    const servicePassword = JWT_SECRET; // Use JWT_SECRET as password

    try {
      await axios.post(`${BACKEND_URL}/auth/register`, {
        email: serviceEmail,
        password: servicePassword,
        name: 'Service User'
      });
      console.log('✅ Service user created successfully');
    } catch (error) {
      if (error.response?.status === 409) {
        console.log('ℹ️ Service user already exists');
      } else {
        console.log('⚠️ Error creating service user:', error.response?.data?.message || error.message);
      }
    }

    // Login with service user
    console.log('🔐 Authenticating with service user...');
    const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
      email: serviceEmail,
      password: servicePassword
    });

    const { access_token } = loginResponse.data;
    console.log('✅ Service authentication successful');

    // Set up axios with auth header
    const authAxios = axios.create({
      baseURL: BACKEND_URL,
      headers: {
        'Authorization': `Bearer ${access_token}`
      }
    });

    // Get or create a project
    console.log('📁 Setting up project...');
    let projectId;
    try {
      const projectsResponse = await authAxios.get('/projects');
      if (projectsResponse.data.length > 0) {
        projectId = projectsResponse.data[0].id;
        console.log('✅ Using existing project:', projectId);
      } else {
        const newProject = await authAxios.post('/projects', {
          name: 'Test Automation Project',
          description: 'Project for automated testing'
        });
        projectId = newProject.data.id;
        console.log('✅ Created new project:', projectId);
      }
    } catch (error) {
      console.error('❌ Error setting up project:', error.response?.data || error.message);
      return;
    }

    // Check if test case already exists
    console.log('🔍 Checking for existing test case...');
    let testCaseId;
    let tcId;
    
    try {
      const testCasesResponse = await authAxios.get(`/projects/${projectId}/test-cases`);
      const existingTestCase = testCasesResponse.data.find(tc => 
        tc.title === 'Verify Successful User Login with Valid Credentials'
      );
      
      if (existingTestCase) {
        testCaseId = existingTestCase.id;
        tcId = existingTestCase.tcId;
        console.log('✅ Found existing test case:', testCaseId);
      }
    } catch (error) {
      console.log('ℹ️ No existing test case found, will create new one');
    }

    // Create test case if it doesn't exist
    if (!testCaseId) {
      console.log('📝 Creating new test case...');
      try {
        const testCaseData = {
          title: 'Verify JWT-based Authentication Test',
          steps: `1. Navigate to login page (${LOGIN_URL})
2. Use JWT_SECRET for authentication instead of credentials
3. Verify successful authentication
4. Check dashboard access`,
          expectation: 'System should authenticate using JWT_SECRET and provide access',
          priority: 'high',
          type: 'functional',
          platform: 'web',
          testCaseType: 'manual',
          automationByAgentq: false
        };
        
        const newTestCase = await authAxios.post(`/projects/${projectId}/test-cases`, testCaseData);
        testCaseId = newTestCase.data.id;
        tcId = newTestCase.data.tcId;
        console.log('✅ Created new test case:', testCaseId);
      } catch (error) {
        console.error('❌ Error creating test case:', error.response?.data || error.message);
        return;
      }
    }

    // Set up automation steps using JWT_SECRET for authentication
    console.log('🤖 Setting up JWT-based automation steps...');
    const automationSteps = [
      {
        step: 1,
        stepName: `Navigate to application (${LOGIN_URL})`,
        action: 'goto',
        target: LOGIN_URL,
        value: '',
        prompt: ''
      },
      {
        step: 2,
        stepName: 'Set JWT token in localStorage for authentication',
        action: 'prompt',
        target: '',
        value: `Set JWT token in browser localStorage using JWT_SECRET: ${JWT_SECRET.substring(0, 10)}...`,
        prompt: `Execute JavaScript to set authentication token in localStorage using the JWT_SECRET for seamless authentication`
      },
      {
        step: 3,
        stepName: 'Navigate to dashboard or protected area',
        action: 'goto',
        target: 'http://localhost:5173/dashboard',
        value: '',
        prompt: ''
      },
      {
        step: 4,
        stepName: 'Verify JWT-based authentication success',
        action: 'prompt',
        target: '',
        value: 'Verify that the application recognizes the JWT token and provides access to protected areas',
        prompt: 'Verify that the application recognizes the JWT token and provides access to protected areas'
      }
    ];

    try {
      await authAxios.post(`/projects/${projectId}/test-cases/tcId/${tcId}/automation`, {
        testCaseId: testCaseId,
        steps: automationSteps
      });
      console.log('✅ Automation steps configured successfully');
    } catch (error) {
      console.error('❌ Error setting up automation steps:', error.response?.data || error.message);
      return;
    }

    // Update test case type to automation
    try {
      await authAxios.patch(`/projects/${projectId}/test-cases/tcId/${tcId}/type`, {
        testCaseType: 'automation'
      });
      console.log('✅ Test case type updated to automation');
    } catch (error) {
      console.log('⚠️ Warning: Could not update test case type:', error.response?.data?.message || error.message);
    }

    // Enable AgentQ automation
    try {
      await authAxios.patch(`/projects/${projectId}/test-cases/tcId/${tcId}/automation-by-agentq`, {
        automationByAgentq: true
      });
      console.log('✅ AgentQ automation enabled');
    } catch (error) {
      console.log('⚠️ Warning: Could not enable AgentQ automation:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 JWT-based test case setup completed successfully!');
    console.log(`📋 Test Case ID: ${testCaseId}`);
    console.log(`🔢 TC ID: ${tcId}`);
    console.log(`🔐 JWT_SECRET: ${JWT_SECRET.substring(0, 10)}...`);
    console.log(`🌐 Application URL: ${LOGIN_URL}`);
    console.log(`🎯 Authentication Method: JWT_SECRET (no credentials needed)`);
    console.log('\n✨ You can now run the JWT-based test automation from the frontend!');

  } catch (error) {
    console.error('❌ Setup failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run the setup
setupTestCase();
