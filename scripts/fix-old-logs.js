#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to fix old test results with invalid GCS URLs
 * This script will update old test results that have GCS URLs pointing to non-existent files
 * and convert them to local storage indicators.
 */

const axios = require('axios');

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3010';
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_change_in_production';

async function fixOldLogs() {
  try {
    console.log('🔧 Fixing old test results with invalid GCS URLs...');
    
    // Generate JWT token for service authentication
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      { 
        sub: 'log-fix-service',
        role: 'service'
      }, 
      JWT_SECRET, 
      { expiresIn: '1h' }
    );

    // Set up axios with auth header
    const authAxios = axios.create({
      baseURL: BACKEND_URL,
      headers: {
        'Authorization': `<PERSON><PERSON> ${token}`
      }
    });

    console.log('📋 Fetching all temp test results...');
    
    // Get all temp test results
    const response = await authAxios.get('/temp-test-results');
    const testResults = response.data;
    
    console.log(`📊 Found ${testResults.length} test results to check`);
    
    let fixedCount = 0;
    let skippedCount = 0;
    
    for (const result of testResults) {
      try {
        // Check if the result has a GCS URL that points to non-existent file
        if (result.logsUrl && result.logsUrl.startsWith('gs://')) {
          console.log(`🔍 Checking: ${result.id} - ${result.logsUrl}`);
          
          // Try to access the logs to see if they exist
          try {
            await authAxios.get(`/temp-test-results/${result.id}/logs`);
            console.log(`✅ Logs accessible for: ${result.id}`);
            skippedCount++;
          } catch (error) {
            if (error.response?.status === 404 || error.response?.status === 500) {
              // Logs don't exist in GCS, convert to local storage indicator
              console.log(`🔄 Converting to local storage: ${result.id}`);
              
              const updateData = {
                logsUrl: `local://temp-test-results/logs/${result.id}`
              };
              
              await authAxios.put(`/temp-test-results/${result.id}`, updateData);
              console.log(`✅ Fixed: ${result.id}`);
              fixedCount++;
            } else {
              console.log(`⚠️ Unexpected error for ${result.id}:`, error.message);
              skippedCount++;
            }
          }
        } else if (result.logsUrl && result.logsUrl.startsWith('local://')) {
          console.log(`✅ Already local: ${result.id}`);
          skippedCount++;
        } else {
          console.log(`ℹ️ No logs URL: ${result.id}`);
          skippedCount++;
        }
      } catch (error) {
        console.error(`❌ Error processing ${result.id}:`, error.message);
        skippedCount++;
      }
    }

    console.log('\n🎉 Fix completed!');
    console.log(`✅ Fixed: ${fixedCount} test results`);
    console.log(`⏭️ Skipped: ${skippedCount} test results`);
    console.log(`📊 Total: ${testResults.length} test results processed`);

  } catch (error) {
    console.error('❌ Fix failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Run the fix
if (require.main === module) {
  fixOldLogs();
}

module.exports = { fixOldLogs };
