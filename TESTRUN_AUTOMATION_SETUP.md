# TestRun Automation Setup

This document explains the setup for running automated tests from the TestRunDetail page.

## Architecture Overview

The TestRun automation system uses a separate WebSocket server and endpoint from the TestAutomation page:

### TestAutomation Page (Existing)
- **WebSocket Server**: Port 3008
- **Storage Endpoint**: `/temp-test-results` (temporary results)
- **Purpose**: Quick test execution and validation

### TestRunDetail Page (New)
- **WebSocket Server**: Port 3009 (separate server)
- **Storage Endpoint**: `/projects/{projectId}/test-runs/{testRunId}/test-results`
- **Purpose**: Formal test execution within test runs

## Components

### 1. WebSocket Server (Port 3009)
- **File**: `../websocket_ai_automation_test_agentq/test-runner-testrun.ts`
- **Purpose**: Handles test execution requests for test runs
- **Features**:
  - Separate from TestAutomation WebSocket server
  - Uses AgentQ API key validation
  - Stores results in test run context

### 2. Test Spec File
- **File**: `../websocket_ai_automation_test_agentq/tests/testrun.spec.ts`
- **Purpose**: Playwright test execution for test run automation
- **Features**:
  - Saves results to `/projects/{projectId}/test-runs/{testRunId}/test-results`
  - Includes authentication token for backend API calls
  - Comprehensive logging and error handling

### 3. Frontend Integration
- **File**: `src/components/project/test_runs/TestRunDetail.vue`
- **Features**:
  - "Run Test" button for automated test cases
  - Real-time WebSocket communication
  - Test execution status and logs
  - Integration with existing test run workflow

## Environment Variables

Add to your `.env` file:

```env
# WebSocket Service for TestRun automation (separate server)
VITE_WEBSOCKET_TESTRUN_URL=ws://localhost:3009
```

## Running the Services

### Start All Services
```bash
# Backend API (port 3010)
cd app_backend_agentq
npm run start:dev

# Core Service (port 3000) - for API key validation
cd websocket_ai_automation_test_agentq
npm run core

# TestAutomation WebSocket (port 3008)
cd websocket_ai_automation_test_agentq
npm run server

# TestRun WebSocket (port 3009) - NEW
cd websocket_ai_automation_test_agentq
npm run testrun

# Frontend (port 5174)
cd app_frontend_agentq
npm run dev
```

### Development Mode
```bash
# For TestRun server with auto-reload
cd websocket_ai_automation_test_agentq
npm run dev:testrun
```

## Usage

1. **Navigate to TestRunDetail page**
   - Go to Projects → Test Runs → Select a test run

2. **Run Automated Tests**
   - Click "Run Test" button on test cases with `automationByAgentq: true`
   - Monitor real-time execution logs
   - Results are saved to the test run automatically

3. **View Results**
   - Test results appear in the test run table
   - Status updates automatically after execution
   - Logs are stored in Google Cloud Storage (if configured)

## API Endpoints

### Test Result Storage
```
POST /projects/{projectId}/test-runs/{testRunId}/test-results
```

**Payload:**
```json
{
  "testCaseId": "string",
  "status": "passed|failed",
  "actualResult": "string",
  "executionTime": "number",
  "logs": ["array of log strings"],
  "notes": "string",
  "screenshotUrl": "string|null",
  "videoUrl": "string|null"
}
```

## Key Differences from TestAutomation

| Feature | TestAutomation | TestRunDetail |
|---------|----------------|---------------|
| WebSocket Port | 3008 | 3009 |
| Storage Endpoint | `/temp-test-results` | `/projects/{projectId}/test-runs/{testRunId}/test-results` |
| Purpose | Quick testing | Formal test runs |
| Data Persistence | Temporary | Permanent in test run |
| Authentication | Optional | Required (JWT token) |

## Troubleshooting

### WebSocket Connection Issues
- Ensure port 3009 is not blocked
- Check if TestRun WebSocket server is running
- Verify `VITE_WEBSOCKET_TESTRUN_URL` environment variable

### Authentication Failures
- Verify AgentQ API key is configured
- Check core service is running on port 3000
- Ensure JWT token is valid

### Test Execution Failures
- Check Playwright dependencies are installed
- Verify test automation steps are properly configured
- Review backend logs for API errors

## Logs and Monitoring

- **WebSocket Server Logs**: Console output from TestRun server
- **Test Execution Logs**: Stored in test results and displayed in UI
- **Backend API Logs**: Check backend server logs for storage issues
- **Google Cloud Storage**: Logs persisted for long-term access (if configured)
