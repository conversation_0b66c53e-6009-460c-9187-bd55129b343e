import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRunsController } from './test-runs.controller';
import { TestRunsService } from './test-runs.service';
import { TestRun } from './test-run.entity';
import { TestResult } from '../test-results/test-result.entity';
import { TestResultHistory } from '../test-results/test-result-history.entity';
import { TestCase } from '../test-cases/test-case.entity';
import { ProjectsModule } from '../projects/projects.module';
import { IntegrationsModule } from '../integrations/integrations.module';
import { Issue } from '../test-results/issue.entity';
import { IssueUpdaterService } from '../test-results/issue-updater.service';
import { UsersModule } from '../users/user.module';
import { TestResultsController } from '../test-results/test-results.controller';
import { StorageService } from '../test-results/storage.service';
import { TestResultsService } from '../test-results/test-results.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([TestRun, TestResult, TestResultHistory, TestCase, Issue]),
    ProjectsModule,
    IntegrationsModule,
    UsersModule,
  ],
  controllers: [TestRunsController, TestResultsController],
  providers: [TestRunsService, IssueUpdaterService, StorageService, TestResultsService],
  exports: [TestRunsService],
})
export class TestRunModule {}
