import { expect } from '@playwright/test';
import { q, test } from 'agentq_web_automation_test';
import axios from 'axios';

// Load test data from environment
const testDataEnv = process.env.TEST_DATA;
let stepsData: any = null;

if (testDataEnv) {
  try {
    stepsData = JSON.parse(testDataEnv);
    console.log(`Loaded test data for: ${stepsData.testCase.title}`);
  } catch (error) {
    console.error('Failed to parse test data:', error);
  }
}

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3010';

// Helper function to save test results to backend
async function saveTestResult(
  projectId: string,
  testRunId: string,
  testCaseId: string,
  status: 'passed' | 'failed',
  actualResult: string,
  executionTime: number,
  logs: string[],
  authToken?: string,
  videoUrl?: string
) {
  try {
    const headers: any = {
      'Content-Type': 'application/json'
    };

    // Add authorization header if auth token is provided
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const payload = {
      testCaseId: testCaseId,
      status: status,
      actualResult: actualResult,
      notes: `Automated test execution - ${status}\n\nExecution time: ${executionTime}ms\n\nLogs:\n${logs.join('\n')}`,
      logs: logs, // Include logs array for backend storage
      screenshotUrl: null,
      videoUrl: videoUrl
    };

    console.log('Saving test result to:', `${BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results`);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    const response = await axios.post(
      `${BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results`,
      payload,
      { headers }
    );

    console.log('✅ Test result saved successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('❌ Failed to save test result:', error.response?.data || error.message);
    throw error;
  }
}

test('Verify Successful User Login with Valid Credentials', async ({ page }, testInfo) => {
  // Set test ID for video recording
  if (stepsData?.testCase?.tcId) {
    testInfo.annotations.push({ type: 'Id', description: stepsData.testCase.tcId });
  }
  const startTime = Date.now();
  let logs: string[] = [];
  let testStatus: 'passed' | 'failed' = 'failed';
  let actualResult = '';

  if (!stepsData || !stepsData.steps) {
    throw new Error('Test data not properly loaded');
  }

  try {
    logs.push(`🚀 Starting test: ${stepsData.testCase.title}`);
    logs.push(`📋 Test Case ID: ${stepsData.testCase.tcId}`);
    logs.push(`📁 Project ID: ${stepsData.projectId}`);
    logs.push(`🏃 Test Run ID: ${stepsData.testRunId}`);

    // Set page context
    (global as any).currentPage = page;

    logs.push(`🔧 Executing ${stepsData.steps.length} automation steps...`);

    // Execute each step
    for (const step of stepsData.steps) {
      if (step.action === 'prompt' && step.value) {
        logs.push(`🔄 Step: prompt ${step.value}`);
        console.log(`Step: prompt ${step.value} ✓`);
        await q(step.value);
        logs.push(`✅ AI prompt executed successfully`);
      } else if (step.action === 'Go to Page' && step.target) {
        logs.push(`🔄 Step: goto ${step.target}`);
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
        logs.push(`✅ Navigated to: ${step.target}`);
      } else if (step.action === 'goto' && step.target) {
        logs.push(`🔄 Step: goto ${step.target}`);
        console.log(`Step: goto ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
        logs.push(`✅ Navigated to: ${step.target}`);
      } else if (step.action === 'navigate' && step.target) {
        logs.push(`🔄 Step: navigate ${step.target}`);
        console.log(`Step: navigate ${step.target} ✓`);
        await page.goto(step.target, { timeout: 30000 });
        logs.push(`✅ Navigated to: ${step.target}`);
      } else if (step.action === 'Fill' && step.target && step.value) {
        logs.push(`🔄 Step: fill ${step.target} with ${step.value}`);
        console.log(`Step: fill ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
        logs.push(`✅ Filled field: ${step.target}`);
      } else if (step.action === 'write' && step.target && step.value) {
        logs.push(`🔄 Step: write ${step.target} with ${step.value}`);
        console.log(`Step: write ${step.target} ${step.value} ✓`);
        await page.fill(step.target, step.value);
        logs.push(`✅ Entered text in: ${step.target}`);
      } else if (step.action === 'Click' && step.target) {
        logs.push(`🔄 Step: click ${step.target}`);
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
        logs.push(`✅ Clicked: ${step.target}`);
      } else if (step.action === 'click' && step.target) {
        logs.push(`🔄 Step: click ${step.target}`);
        console.log(`Step: click ${step.target} ✓`);
        await page.click(step.target);
        logs.push(`✅ Clicked: ${step.target}`);
      } else if (step.action === 'assertText' && step.target && step.value) {
        logs.push(`🔄 Step: assertText ${step.target} should contain ${step.value}`);
        console.log(`Step: assertText ${step.target} ${step.value} ✓`);
        await expect(page.locator(step.target)).toHaveText(step.value);
        logs.push(`✅ Text assertion passed: ${step.target}`);
      } else {
        logs.push(`⚠️ Step: ${step.action} - Skipped (unsupported action)`);
        console.log(`Step: ${step.action} - Skipped (unsupported action)`);
      }
    }

    testStatus = 'passed';
    actualResult = 'Test completed successfully according to automation steps';
    logs.push('🎉 Test completed successfully');

  } catch (error: any) {
    testStatus = 'failed';
    actualResult = `Test failed: ${error.message}`;
    logs.push(`❌ Test failed: ${error.message}`);
    console.error('Test failed:', error);

    // Capture screenshot immediately when test fails
    try {
      const projectId = stepsData?.projectId || 'unknown-project';
      const testRunId = stepsData?.testRunId || 'unknown-testrun';
      const tcId = stepsData?.testCase?.tcId || 'unknown-tc';

      const fs = require('fs');
      const screenshotDir = `./test-results/${projectId}/${testRunId}/${tcId}`;

      if (!fs.existsSync(screenshotDir)) {
        fs.mkdirSync(screenshotDir, { recursive: true });
      }

      const screenshotPath = `${screenshotDir}/screenshot.png`;
      await page.screenshot({ path: screenshotPath, fullPage: true });
      logs.push(`📸 Failure screenshot saved to: ${screenshotPath}`);
    } catch (screenshotError) {
      console.error('Failed to save failure screenshot:', screenshotError);
      logs.push(`❌ Failed to save failure screenshot: ${screenshotError}`);
    }
  } finally {
    const endTime = Date.now();
    const executionTime = endTime - startTime;

    logs.push(`⏱️ Execution time: ${executionTime}ms`);

    // Save video with custom path for TestRunDetail: ./test-results/{projectId}/{testRunId}/{tcId}
    try {
      const projectId = stepsData?.projectId || 'unknown-project';
      const testRunId = stepsData?.testRunId || 'unknown-testrun';
      const tcId = stepsData?.testCase?.tcId || 'unknown-tc';

      if (page?.video()) {
        const videoPath = `./test-results/${projectId}/${testRunId}/${tcId}/video.webm`;
        await page.video()?.saveAs(videoPath);
        // logs.push(`📹 Video saved to: ${videoPath}`);
      }
    } catch (videoError) {
      console.error('Failed to save video:', videoError);
      // logs.push(`❌ Failed to save video: ${videoError}`);
    }

    // Save screenshot with custom path for TestRunDetail: ./test-results/{projectId}/{testRunId}/{tcId}
    try {
      const projectId = stepsData?.projectId || 'unknown-project';
      const testRunId = stepsData?.testRunId || 'unknown-testrun';
      const tcId = stepsData?.testCase?.tcId || 'unknown-tc';

      // Create directory structure if it doesn't exist
      const fs = require('fs');
      const path = require('path');
      const screenshotDir = `./test-results/${projectId}/${testRunId}/${tcId}`;

      if (!fs.existsSync(screenshotDir)) {
        fs.mkdirSync(screenshotDir, { recursive: true });
      }

      const screenshotPath = `${screenshotDir}/screenshot.png`;
      await page.screenshot({ path: screenshotPath, fullPage: true });
      // logs.push(`📸 Screenshot saved to: ${screenshotPath}`);
    } catch (screenshotError) {
      console.error('Failed to save screenshot:', screenshotError);
      // logs.push(`❌ Failed to save screenshot: ${screenshotError}`);
    }

    // Save test result to backend
    try {
      await saveTestResult(
        stepsData.projectId,
        stepsData.testRunId,
        stepsData.testCase.id,
        testStatus,
        actualResult,
        executionTime,
        logs,
        stepsData.authToken
      );
      logs.push('💾 Test result saved to backend');
    } catch (saveError: any) {
      logs.push(`❌ Failed to save test result: ${saveError.message}`);
      console.error('Failed to save test result:', saveError);
    }

    // Print final logs
    console.log('\n=== Test Execution Summary ===');
    logs.forEach(log => console.log(log));
    console.log('===============================\n');
  }

  // Assert based on test status
  if (testStatus === 'failed') {
    throw new Error(actualResult);
  }
});
