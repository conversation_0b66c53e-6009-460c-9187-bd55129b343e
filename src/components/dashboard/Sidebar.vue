<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../../stores/auth';
import axios from 'axios';

const props = defineProps<{
  sidebarCollapsed: boolean;
  activeSetting: 'general' | 'api' | 'integrations' | null;
}>();

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const emit = defineEmits<{
  'update:sidebarCollapsed': [value: boolean];
  'update:activeSetting': [value: 'general' | 'api' | 'integrations' | null];
  'logout': [];
}>();

// Check if current user is superadmin
const isSuperAdmin = computed(() => {
  return authStore.user?.role === 'superadmin';
});

// Check if Atlassian integration is set up
const hasAtlassianIntegration = ref(false);

const checkAtlassianIntegration = async () => {
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/integrations`);
    const integrations = response.data;
    hasAtlassianIntegration.value = integrations.some(
      (integration: any) => integration.type === 'atlassian' && integration.jiraUrl
    );
  } catch (error) {
    console.error('Failed to check Atlassian integration:', error);
    hasAtlassianIntegration.value = false;
  }
};

const toggleSidebar = () => {
  emit('update:sidebarCollapsed', !props.sidebarCollapsed);
};

const setActiveSetting = (setting: 'general' | 'api' | 'integrations' | null) => {
  emit('update:activeSetting', setting);
};

const handleMenuClick = (route: string | null) => {
  emit('update:activeSetting', null);
  if (route) {
    router.push(route);
  }
};

const logout = () => {
  emit('logout');
};

// Compute whether we're in a project detail view
const isProjectDetail = computed(() => {
  return (route.name?.toString().startsWith('project-') || route.name === 'test-automation') && route.params.id;
});

// Get current project ID
const currentProjectId = computed(() => route.params.id as string);

// Handle project menu navigation
const handleProjectMenu = (path: string) => {
  emit('update:activeSetting', null);
    router.push(`/projects/${currentProjectId.value}/${path}`);

};

const handleLogoClick = (event: MouseEvent) => {
  event.stopPropagation(); // Stop event propagation
  router.push('/'); // Navigate to the home page
};

// Handle integration status change events
const handleIntegrationStatusChange = () => {
  checkAtlassianIntegration();
};

// Check for Atlassian integration when component mounts
onMounted(() => {
  checkAtlassianIntegration();

  // Add event listener for integration status changes
  window.addEventListener('integration-status-changed', handleIntegrationStatusChange);
});

// Remove event listener when component is unmounted
onUnmounted(() => {
  window.removeEventListener('integration-status-changed', handleIntegrationStatusChange);
});
</script>

<template>
  <aside class="sidebar" :class="{ 'collapsed': sidebarCollapsed }">
    <div class="sidebar-header">
      <a href="/" class="logo-link" @click.self.prevent="handleLogoClick">
      <h1 class="logo" v-if="!sidebarCollapsed">AgentQ</h1>
      <svg v-else class="magnifying-glass" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="currentColor">
        <path d="M10 18a7.952 7.952 0 004.897-1.688l4.396 4.396 1.414-1.414-4.396-4.396A7.952 7.952 0 0018 10c0-4.411-3.589-8-8-8s-8 3.589-8 8 3.589 8 8 8zm0-14c3.309 0 6 2.691 6 6s-2.691 6-6 6-6-2.691-6-6 2.691-6 6-6z"></path>
      </svg>
    </a>
      <button class="toggle-button" @click="toggleSidebar">
        <span v-if="sidebarCollapsed">›</span>
        <span v-else>‹</span>
      </button>

    </div>

    <div class="sidebar-content">
      <!-- Main menu items -->
      <nav class="sidebar-menu">
        <!-- Show Projects and Generate Test Cases when not in project detail -->
        <template v-if="!isProjectDetail">
          <a
            href="#"
            class="menu-item"
            :class="{ active: $route.path === '/projects' }"
            @click.prevent="handleMenuClick('/projects')"
          >
            <span class="menu-icon">📁</span>
            <span class="menu-text">Projects</span>
          </a>
        </template>

        <!-- Show Test Cases and Test Runs when in project detail -->
        <template v-else>
          <a
            href="#"
            class="menu-item"
            :class="{ active: $route.name === 'project-test-cases' || $route.name === 'test-automation' }"
            @click.prevent="handleProjectMenu('test-cases')"
          >
            <span class="menu-icon">📝</span>
            <span class="menu-text">Test Cases</span>
          </a>
          <a
            href="#"
            class="menu-item"
            :class="{ active: $route.name === 'project-test-runs' }"
            @click.prevent="handleProjectMenu('test-runs')"
          >
            <span class="menu-icon">▶️</span>
            <span class="menu-text">Test Runs</span>
          </a>
          <a
            href="#"
            class="menu-item"
            :class="{
              active: $route.name === 'project-issues',
              'warning': !hasAtlassianIntegration
            }"
            @click.prevent="handleProjectMenu('issues')"
            :title="hasAtlassianIntegration ? 'View Issues' : 'Atlassian integration required'"
          >
            <span class="menu-icon">🐞</span>
            <span class="menu-text">Issues</span>
            <span v-if="!hasAtlassianIntegration" class="menu-warning">⚠️</span>
          </a>
        </template>
      </nav>

      <!-- Bottom menu items -->
      <div class="sidebar-footer">
        <a v-if="isSuperAdmin" href="#" class="menu-item" :class="{ active: activeSetting === 'general' }" @click.prevent="setActiveSetting('general')">
          <span class="menu-icon">⚙️</span>
          <span class="menu-text">Settings</span>
        </a>
        <a v-if="activeSetting !== null" href="#" class="menu-item" :class="{ active: activeSetting === 'api' }" @click.prevent="setActiveSetting('api')">
          <span class="menu-icon">🔑</span>
          <span class="menu-text">API Key</span>
        </a>
        <a v-if="activeSetting !== null" href="#" class="menu-item" :class="{ active: activeSetting === 'integrations' }" @click.prevent="setActiveSetting('integrations')">
          <span class="menu-icon">🔗</span>
          <span class="menu-text">3rd Party Integration</span>
        </a>
        <!-- <a href="#" class="menu-item" :class="{ active: $route.path === '/system-health' }" @click.prevent="handleMenuClick('/system-health')">
          <span class="menu-icon">🔍</span>
          <span class="menu-text">System Health</span>
        </a> -->
        <a href="#" class="menu-item" @click.prevent="logout">
          <span class="menu-icon">🚪</span>
          <span class="menu-text">Sign Out</span>
        </a>
      </div>
    </div>
  </aside>
</template>

<style lang="scss" scoped>
.sidebar {
  width: 220px;
  background-color: white;
  color: black;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;

  &.collapsed {
    width: 70px;

    .menu-text {
      display: none;
    }

    .logo {
      font-size: 18px;
    }

    .sidebar-header {
      padding: 16px 10px;
    }
  }
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.logo {
  font-size: 22px;
  font-weight: bold;
  color: #e94560;
  margin: 0;
}

.magnifying-glass {
  width: 32px;
  height: 32px;
  fill: #e94560;
}

.toggle-button {
  background: none;
  border: none;
  color: black;
  font-size: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 4px;

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.sidebar-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  overflow-y: auto;
}

.sidebar-menu {
  padding: 20px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s;
  position: relative;

  &:hover, &.active {
    background-color: #f9fafb;
    color: black;
  }

  &.active {
    border-left: 3px solid #e94560;
    background-color: #f9fafb;
    font-weight: 500;
  }

  &.warning {
    position: relative;

    &:hover {
      background-color: #fff8e6;
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      height: 100%;
      background-color: #f59e0b;
    }
  }

  .menu-warning {
    position: absolute;
    right: 12px;
    font-size: 14px;
  }
}

.menu-icon {
  margin-right: 12px;
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.sidebar-menu .menu-item .menu-icon {
  margin-right: 10px;
}

.sidebar-footer {
  margin-top: auto;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 10px;
}

// Responsive styles
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    z-index: 1000;

    &.collapsed {
      transform: translateX(-100%);
    }
  }

  .sidebar.collapsed.mobile-open {
    transform: translateX(0);
    width: 260px;

    .menu-text {
      display: inline;
    }
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }
}
</style>
