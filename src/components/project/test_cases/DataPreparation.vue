<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import axios from 'axios';

interface ApiKey {
  id: string;
  provider: string;
  apiKey: string;
}

interface Integration {
  id: string;
  type: string;
  email: string;
  apiToken: string;
}

const props = defineProps<{
  loading: boolean;
  progress: number;
  processingStatus: string;
  error?: string;
  isProcessing?: boolean;
}>();

const emit = defineEmits<{
  'submit': [data: {
    name: string;
    file: File[] | File | null;
    apiKey: string;
    publicUrl?: string;
    selectedIntegration?: string;
    privateUrl?: string;
    integrationType?: string;
    integrationEmail?: string;
    integrationApiToken?: string;
  }];
}>();

const activeTab = ref('upload');
const apiKeys = ref<ApiKey[]>([]);
const integrations = ref<Integration[]>([]);
const name = ref('');
const selectedApiKey = ref('');
const selectedIntegration = ref('');
const file = ref<File[]>([]);
const publicUrl = ref('');
const privateUrl = ref('');
const error = ref('');

// Track which tabs have data
const tabStatus = ref({
  upload: false,
  public: false,
  private: false
});

const fetchApiKeys = async () => {
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`);
    apiKeys.value = response.data;
  } catch (err) {
    console.error('Failed to fetch API keys:', err);
  }
};

const fetchIntegrations = async () => {
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/integrations`);
    integrations.value = response.data;
  } catch (err) {
    console.error('Failed to fetch integrations:', err);
  }
};

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    const newFiles = Array.from(input.files);
    if (file.value.length + newFiles.length > 3) {
      error.value = 'Maximum 3 files can be uploaded.';
      // Clear the input so that the extra files are not kept
      input.value = '';
      return;
    }
    error.value = ''; // Clear any previous error
    file.value.push(...newFiles);
    tabStatus.value.upload = true;
  }
  // Clear the input so that the same file can be selected again if needed
  const fileInput = ref<HTMLInputElement | null>(null);
  if (fileInput.value) {
    fileInput.value.value = '';
  }
};

const removeFile = (index: number) => {
  file.value.splice(index, 1);
  if (file.value.length === 0) {
    tabStatus.value.upload = false;
  }
};

// Update tab status when data changes
watch(publicUrl, (val) => {
  tabStatus.value.public = !!val;
});

watch(privateUrl, (val) => {
  tabStatus.value.private = !!val && !!selectedIntegration.value;
});

watch(selectedIntegration, (val) => {
  tabStatus.value.private = !!val && !!privateUrl.value;
});

const handleSubmit = async () => {
  if (props.loading) return;

  try {
    // Build the data object with all active tabs' data
    const submissionData = {
      name: name.value,
      apiKey: selectedApiKey.value,
      file: null as File[] | null,
      publicUrl: undefined as string | undefined,
      selectedIntegration: undefined as string | undefined,
      privateUrl: undefined as string | undefined,
      integrationType: undefined as string | undefined,
      integrationEmail: undefined as string | undefined,
      integrationApiToken: undefined as string | undefined,
    };

    // Check if file upload tab has data
    if (tabStatus.value.upload && file.value.length > 0) {
      submissionData.file = file.value;
    } else if (activeTab.value === 'upload') {
      error.value = "Please select one or more files.";
      return;
    }

    // Check if public URL tab has data
    if (tabStatus.value.public) {
      submissionData.publicUrl = publicUrl.value;
    }

    // Check if private URL tab has data
    if (tabStatus.value.private) {
      const selectedIntegrationObject = integrations.value.find(
        (integration) => integration.id === selectedIntegration.value
      );

      if (!selectedIntegrationObject) {
        if (activeTab.value === 'private') {
          error.value = 'Selected integration not found.';
          return;
        }
      } else {
        submissionData.selectedIntegration = selectedIntegration.value;
        submissionData.privateUrl = privateUrl.value;
        submissionData.integrationType = selectedIntegrationObject.type;
        submissionData.integrationEmail = selectedIntegrationObject.email;
        submissionData.integrationApiToken = selectedIntegrationObject.apiToken;
      }
    }

    // Make sure at least one tab has data
    if (!tabStatus.value.upload && !tabStatus.value.public && !tabStatus.value.private) {
      error.value = "Please provide at least one data source (file upload, public URL, or private URL).";
      return;
    }

    error.value = '';
    emit('submit', submissionData);
  } catch (err) {
    console.error('Submission failed:', err);
    error.value = 'An error occurred while processing your request.';
  }
};

onMounted(() => {
  fetchApiKeys();
  fetchIntegrations();
});
</script>

<template>
  <div>
    <h2 class="title">Select your preferred method to generate test cases using AI.</h2>

    <div class="tabs">
      <button
        :class="['tab-button', { active: activeTab === 'upload', 'has-data': tabStatus.upload }]"
        @click="activeTab = 'upload'"
      >
        Upload File
      </button>
      <button
        :class="['tab-button', { active: activeTab === 'public', 'has-data': tabStatus.public }]"
        @click="activeTab = 'public'"
      >
        Public URL
      </button>
      <button
        :class="['tab-button', { active: activeTab === 'private', 'has-data': tabStatus.private }]"
        @click="activeTab = 'private'"
      >
        Private URL
      </button>
    </div>

    <form @submit.prevent="handleSubmit" class="form-container">
      <!-- Upload File Tab -->
      <div v-if="activeTab === 'upload'" class="tab-content">
        <div class="section">
          <h3>Upload Document</h3>
          <p class="supported-formats">
            Supported formats: .doc, .docx, .md, .txt, .pdf, .html, .xls, .xlsx, .csv
          </p>

          <div class="form-group">
            <label>Name</label>
            <input
              v-model="name"
              type="text"
              placeholder="Enter a name for this test case"
              required
            />
          </div>

          <div class="form-group">
            <label>Select API Key</label>
            <select v-model="selectedApiKey" required>
              <option value="">Select an API Key</option>
              <option v-for="key in apiKeys" :key="key.id" :value="key.apiKey">
                {{ key.provider }}
              </option>
            </select>
          </div>

          <div class="file-upload">
            <div v-if="file.length === 0" class="file-upload-placeholder">
              <div class="file-upload-icon">📄</div>
              <p>Drag and drop files here or <label for="file-input" class="file-upload-button-label">click to select</label></p>
            </div>
            <div v-else class="file-list-preview">
              <div v-for="(f, index) in file" :key="index" class="file-item">
                <span>{{ f.name }}</span>
                <button type="button" class="remove-file-button" @click="removeFile(index)">
                  ❌
                </button>
              </div>
            </div>
            <input
              type="file"
              ref="fileInput"
              id="file-input"
              @change="handleFileChange"
              accept=".doc,.docx,.md,.txt,.pdf,.html,.xls,.xlsx,.csv"
              multiple
            />
          </div>
        </div>
      </div>

      <!-- Public URL Tab -->
      <div v-if="activeTab === 'public'" class="tab-content">
        <div class="section">
          <div class="form-group">
            <label>Name</label>
            <input
              v-model="name"
              type="text"
              placeholder="Enter a name for this test case"
              required
            />
          </div>

          <div class="form-group">
            <label>Select API Key</label>
            <select v-model="selectedApiKey" required>
              <option value="">Select an API Key</option>
              <option v-for="key in apiKeys" :key="key.id" :value="key.apiKey">
                {{ key.provider }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>URL</label>
            <input
              v-model="publicUrl"
              type="url"
              placeholder="Enter public URL"
            />
          </div>
        </div>
      </div>

      <!-- Private URL Tab -->
      <div v-if="activeTab === 'private'" class="tab-content">
        <div class="section">
          <div class="form-group">
            <label>Name</label>
            <input
              v-model="name"
              type="text"
              placeholder="Enter a name for this test case"
              required
            />
          </div>

          <div class="form-group">
            <label>Select API Key</label>
            <select v-model="selectedApiKey" required>
              <option value="">Select an API Key</option>
              <option v-for="key in apiKeys" :key="key.id" :value="key.apiKey">
                {{ key.provider }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>Select 3rd Party Integration</label>
            <select v-model="selectedIntegration">
              <option value="">Select an integration</option>
              <option v-for="integration in integrations" :key="integration.id" :value="integration.id">
                {{ integration.type }} - {{ integration.email }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label>URL</label>
            <input
              v-model="privateUrl"
              type="url"
              placeholder="Enter private URL"
            />
          </div>
        </div>
      </div>

      <div v-if="error" class="error-message">
        <div class="flex items-center">
          <span class="error-icon">⚠️</span>
          {{ error }}
        </div>
      </div>

      <div v-if="props.error" class="error-message">
        <div class="flex items-center">
          <span class="error-icon">⚠️</span>
          {{ props.error }}
        </div>
      </div>

      <div v-if="tabStatus.upload || tabStatus.public || tabStatus.private" class="active-sources">
        <h4>Active data sources:</h4>
        <div class="source-tags">
          <span v-if="tabStatus.upload" class="source-tag">File Upload</span>
          <span v-if="tabStatus.public" class="source-tag">Public URL</span>
          <span v-if="tabStatus.private" class="source-tag">Private URL</span>
        </div>
      </div>

      <!-- Process Data Button - Only shown when not loading or processing -->
      <button v-if="!props.loading && !props.isProcessing" type="submit" class="generate-button">
        Process Data
      </button>

      <!-- Processing Progress - Replaces the button when processing -->
      <div v-if="props.loading || props.isProcessing" class="processing-progress">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${props.progress}%` }"
          ></div>
        </div>
        <div class="progress-status">{{ props.processingStatus }}</div>
        <div class="progress-percentage">{{ Math.round(props.progress) }}%</div>
      </div>
    </form>
  </div>
</template>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  color: #374151;
  margin-bottom: 24px;
  font-weight: 500;
}

.tabs {
  display: flex;
  gap: 16px;
  margin-bottom: 32px;
}

.tab-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;

  &:hover {
    background-color: #f3f4f6;
  }

  &.active {
    background-color: #e94560;
    color: white;
  }

  &.has-data:not(.active)::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #10b981;
  }
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.section {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 24px;

  h3 {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
  }
}

.supported-formats {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }

  input, select {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }

    &::placeholder {
      color: #9ca3af;
    }
  }
}

.file-upload {
  background-color: white;
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  position: relative;
  overflow: hidden;

  .file-upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 16px 0;
    cursor: pointer;
  }

  .file-upload-icon {
    font-size: 48px;
    color: #9ca3af;
  }

  .file-upload-placeholder p {
    color: #6b7280;
    font-size: 16px;
  }

  .file-upload-button-label {
    color: #e94560;
    cursor: pointer;
  }

  .file-list-preview {
    margin-top: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 8px;
    text-align: left;
  }

  .file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    border-bottom: 1px solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    span {
      flex-grow: 1;
      margin-right: 16px;
      font-size: 14px;
      color: #374151;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .remove-file-button {
      background: none;
      border: none;
      cursor: pointer;
      color: #ef4444;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.2s;
      z-index: 2;

      &:hover {
        background-color: #fef2f2;
      }
    }
  }

  input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 1;
    width: 100%;
    height: calc(100% - 16px - 8px - 16px);
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;

  .error-icon {
    font-size: 18px;
  }
}

.processing-progress {
  width: 100%;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  margin-bottom: 24px;

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
  }

  .progress-fill {
    height: 100%;
    background-color: #e94560;
    transition: width 0.3s ease;
  }

  .progress-status {
    text-align: center;
    color: #374151;
    font-size: 14px;
    margin-bottom: 4px;
    font-weight: 500;
  }

  .progress-percentage {
    text-align: center;
    color: #6b7280;
    font-size: 12px;
  }
}

.active-sources {
  background-color: #f9fafb;
  border-radius: 8px;
  padding: 16px;

  h4 {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }

  .source-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .source-tag {
      background-color: #e5e7eb;
      color: #374151;
      padding: 6px 12px;
      border-radius: 16px;
      font-size: 12px;
      font-weight: 500;

      &:nth-child(1) {
        background-color: #dbeafe;
        color: #1e40af;
      }

      &:nth-child(2) {
        background-color: #dcfce7;
        color: #166534;
      }

      &:nth-child(3) {
        background-color: #fef3c7;
        color: #92400e;
      }
    }
  }
}

.generate-button {
  width: 100%;
  padding: 14px;
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #d63553;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}
</style>