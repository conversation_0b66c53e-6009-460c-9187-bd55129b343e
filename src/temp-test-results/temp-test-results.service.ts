import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TempTestResult } from './temp-test-result.entity';
import { CreateTempTestResultDto } from './dto/create-temp-test-result.dto';
import { StorageService } from './storage.service';

@Injectable()
export class TempTestResultsService {
  private readonly logger = new Logger(TempTestResultsService.name);

  constructor(
    @InjectRepository(TempTestResult)
    private tempTestResultRepository: Repository<TempTestResult>,
    private storageService: StorageService,
  ) {}

  /**
   * Create a new temp test result with hybrid storage
   * @param createDto - The test result data
   * @returns Promise<TempTestResult>
   */
  async create(createDto: CreateTempTestResultDto): Promise<TempTestResult> {
    try {
      // Create the database record first
      const tempTestResult = this.tempTestResultRepository.create({
        testCaseId: createDto.testCaseId,
        tcId: createDto.tcId,
        projectId: createDto.projectId,
        status: createDto.status,
        summary: createDto.summary,
        duration: createDto.duration,
        testData: createDto.testData,
        errorMessage: createDto.errorMessage,
        executedAt: createDto.executedAt ? new Date(createDto.executedAt) : new Date(),
      });

      const savedResult = await this.tempTestResultRepository.save(tempTestResult);

      // Upload logs to Google Cloud Storage if provided and enabled
      if (createDto.logs && createDto.logs.length > 0) {
        try {
          const logsUrl = await this.storageService.uploadLogs(savedResult.id, createDto.logs, 'temp-test-results');
          savedResult.logsUrl = logsUrl;
          await this.tempTestResultRepository.save(savedResult);

          if (logsUrl.startsWith('local://')) {
            this.logger.log(`Test result created with logs stored locally: ${savedResult.id} (${createDto.logs.length} entries)`);
          } else {
            this.logger.log(`Test result created with logs stored in GCS: ${savedResult.id}`);
          }
        } catch (error) {
          this.logger.error(`Failed to process logs for test result ${savedResult.id}:`, error);
          // Continue without failing the entire operation
        }
      }

      return savedResult;
    } catch (error) {
      this.logger.error('Failed to create temp test result:', error);
      throw error;
    }
  }

  /**
   * Find all temp test results for a test case
   * @param testCaseId - The test case ID
   * @returns Promise<TempTestResult[]>
   */
  async findByTestCase(testCaseId: string): Promise<TempTestResult[]> {
    return this.tempTestResultRepository.find({
      where: { testCaseId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find the most recent temp test result for a test case
   * @param testCaseId - The test case ID
   * @returns Promise<TempTestResult | null>
   */
  async findLatestByTestCase(testCaseId: string): Promise<TempTestResult | null> {
    const results = await this.findByTestCase(testCaseId);
    return results.length > 0 ? results[0] : null;
  }

  /**
   * Find all temp test results for a project
   * @param projectId - The project ID
   * @returns Promise<TempTestResult[]>
   */
  async findByProject(projectId: string): Promise<TempTestResult[]> {
    return this.tempTestResultRepository.find({
      where: { projectId },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find a specific temp test result by ID
   * @param id - The test result ID
   * @returns Promise<TempTestResult>
   */
  async findOne(id: string): Promise<TempTestResult> {
    const result = await this.tempTestResultRepository.findOne({
      where: { id },
    });

    if (!result) {
      throw new NotFoundException(`Temp test result with ID ${id} not found`);
    }

    return result;
  }

  /**
   * Get logs for a specific test result
   * @param id - The test result ID
   * @returns Promise<string[]>
   */
  async getLogs(id: string): Promise<string[]> {
    const result = await this.findOne(id);

    if (!result.logsUrl) {
      this.logger.warn(`No logs URL found for test result ${id}`);
      return [];
    }

    try {
      return await this.storageService.downloadLogs(result.logsUrl);
    } catch (error) {
      this.logger.error(`Failed to download logs for test result ${id}:`, error);
      return [];
    }
  }

  /**
   * Delete a temp test result and its logs
   * @param id - The test result ID
   * @returns Promise<void>
   */
  async remove(id: string): Promise<void> {
    const result = await this.findOne(id);

    // Delete logs from Google Cloud Storage if they exist
    if (result.logsUrl) {
      try {
        await this.storageService.deleteLogs(result.logsUrl);
      } catch (error) {
        this.logger.error(`Failed to delete logs for test result ${id}:`, error);
        // Continue with database deletion even if GCS deletion fails
      }
    }

    // Delete the database record
    await this.tempTestResultRepository.remove(result);
    this.logger.log(`Temp test result deleted: ${id}`);
  }

  /**
   * Clean up old test results (older than specified days)
   * @param daysOld - Number of days old
   * @returns Promise<number> - Number of records deleted
   */
  async cleanupOldResults(daysOld: number = 30): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const oldResults = await this.tempTestResultRepository.find({
      where: {
        createdAt: { $lt: cutoffDate } as any,
      },
    });

    let deletedCount = 0;
    for (const result of oldResults) {
      try {
        await this.remove(result.id);
        deletedCount++;
      } catch (error) {
        this.logger.error(`Failed to delete old test result ${result.id}:`, error);
      }
    }

    this.logger.log(`Cleaned up ${deletedCount} old test results`);
    return deletedCount;
  }

  /**
   * Get video URL for a specific test result
   * @param id - The test result ID
   * @returns Promise<string> - The signed URL for the video
   */
  async getVideoUrl(id: string): Promise<string> {
    const result = await this.findOne(id);

    if (!result.videoUrl) {
      this.logger.warn(`No video URL found for test result ${id}`);
      return '';
    }

    try {
      // Generate a signed URL for direct access
      return await this.storageService.getSignedUrl(result.videoUrl);
    } catch (error) {
      this.logger.error(`Failed to get signed URL for video ${id}:`, error);
      return '';
    }
  }

  /**
   * Upload video for a test result
   * @param id - The test result ID
   * @param videoBuffer - The video file buffer
   * @param contentType - The video content type
   * @returns Promise<string> - The GCS URL
   */
  async uploadVideo(id: string, videoBuffer: Buffer, contentType: string): Promise<string> {
    try {
      const result = await this.findOne(id);
      
      // Upload video to GCS
      const videoUrl = await this.storageService.uploadVideo(id, videoBuffer, contentType);
      
      // Update the test result with the video URL
      result.videoUrl = videoUrl;
      await this.tempTestResultRepository.save(result);
      
      return videoUrl;
    } catch (error) {
      this.logger.error(`Failed to upload video for test result ${id}:`, error);
      throw error;
    }
  }

  /**
   * Update an existing test result
   * @param id - The test result ID
   * @param updateDto - The update data
   * @returns Promise<TempTestResult>
   */
  async update(id: string, updateDto: any): Promise<TempTestResult> {
    try {
      const existingResult = await this.findOne(id);
      
      // Update basic fields
      Object.assign(existingResult, {
        status: updateDto.status,
        errorMessage: updateDto.errorMessage,
        executedAt: updateDto.executedAt,
        duration: updateDto.duration,
        summary: updateDto.summary,
        testData: updateDto.testData
      });
      
      // Save the updated result
      const savedResult = await this.tempTestResultRepository.save(existingResult);
      
      // Update logs if provided
      if (updateDto.logs && updateDto.logs.length > 0) {
        try {
          // If there's an existing logs URL, delete the old logs first
          if (existingResult.logsUrl) {
            await this.storageService.deleteLogs(existingResult.logsUrl);
          }
          
          // Upload new logs
          const logsUrl = await this.storageService.uploadLogs(savedResult.id, updateDto.logs, 'temp-test-results');
          savedResult.logsUrl = logsUrl;
          await this.tempTestResultRepository.save(savedResult);
          
          this.logger.log(`Test result updated with logs stored in GCS: ${savedResult.id}`);
        } catch (error) {
          this.logger.error(`Failed to update logs for test result ${savedResult.id}:`, error);
          // Continue without failing the entire operation
        }
      }
      
      return savedResult;
    } catch (error) {
      this.logger.error(`Failed to update temp test result ${id}:`, error);
      throw error;
    }
  }
}
