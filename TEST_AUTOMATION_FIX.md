# Test Automation Fix Guide

## Problem Identified

The test automation was failing because:

1. ✅ **AgentQ API Key Authentication**: Working correctly (status 201)
2. ❌ **Application Login**: Failing with "Invalid credentials" (status 401)

The issue was that the test case was trying to login to the application with invalid or non-existent credentials.

## Solution Applied

### 1. Backend Changes

**File: `src/main.ts`**
- ✅ Enabled default user creation
- ✅ Creates user: `<EMAIL>` with password: `agentq`

### 2. Test Case Setup Script

**File: `scripts/setup-test-case.js`**
- ✅ Creates test user account if needed
- ✅ Sets up proper test case with valid credentials
- ✅ Configures automation steps with correct selectors
- ✅ Enables AgentQ automation

## How to Fix the Issue

### Step 1: Restart the Backend
```bash
# Stop the backend if running
# Restart the backend to create the default user
npm run start:dev
```

### Step 2: Run the Setup Script
```bash
# Make the script executable
chmod +x scripts/setup-test-case.js

# Run the setup script
node scripts/setup-test-case.js
```

### Step 3: Verify the Setup
1. Check that the default user was created in the logs
2. Verify the test case is properly configured
3. Run the test automation from the frontend

## Test Credentials

- **Email**: `<EMAIL>`
- **Password**: `agentq`
- **Login URL**: `http://localhost:5173/login`

## Test Case Configuration

The script will create/update a test case with these automation steps:

1. **Navigate to login page** (`goto` action)
   - Target: `http://localhost:5173/login`

2. **Enter email** (`write` action)
   - Target: `#email`
   - Value: `<EMAIL>`

3. **Enter password** (`write` action)
   - Target: `#password`
   - Value: `agentq`

4. **Click login button** (`click` action)
   - Target: `button[type="submit"]`

5. **Verify successful login** (`prompt` action)
   - AI verification of successful login and dashboard redirect

## Verification Steps

1. **Backend Logs**: Should show "Default user created: <EMAIL> / agentq"
2. **Frontend**: Navigate to test automation page
3. **Test Run**: Click "Run Test" button
4. **Expected Result**: Test should pass with successful login

## Troubleshooting

### If the test still fails:

1. **Check Frontend URL**: Ensure the frontend is running on `http://localhost:5173`
2. **Verify User Creation**: Check backend logs for user creation
3. **Test Manual Login**: Try logging in manually with the credentials
4. **Check Selectors**: Verify the CSS selectors match the login form

### Common Issues:

- **Frontend not running**: Start with `npm run dev`
- **Backend not running**: Start with `npm run start:dev`
- **Database issues**: Check database connection
- **Port conflicts**: Ensure ports 3010 (backend) and 5173 (frontend) are available

## Next Steps

After applying this fix:

1. ✅ The test automation should work correctly
2. ✅ Login credentials will be valid
3. ✅ Test execution will complete successfully
4. ✅ You can create additional test cases with proper credentials

## Additional Notes

- The default user is created automatically on backend startup
- The setup script can be run multiple times safely
- Test credentials can be changed by modifying the script
- Additional test users can be created through the registration endpoint
