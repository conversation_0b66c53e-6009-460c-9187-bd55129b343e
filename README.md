# AgentQ Frontend Microservice

This is the frontend microservice for AgentQ, built with Vue.js.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file with the following content:
```
VITE_BACKEND_URL=http://localhost:3003
VITE_INTEGRATION_URL=http://localhost:3004
```

3. Start the development server:
```bash
npm run dev
```

4. Build for production:
```bash
npm run build
```

## Features

- User authentication (login/register)
- Project management
- Test case management
- Test run execution and reporting
- Integration with Jira (via the external integration service)

## Port

The frontend runs on port 5173 in development mode.
