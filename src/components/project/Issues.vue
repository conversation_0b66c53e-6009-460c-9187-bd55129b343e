<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import FilterModal from './issues/FilterModal.vue';
import ActiveFilters from './issues/ActiveFilters.vue';

interface Issue {
  id: string;
  defectId: number;
  testRunId: string;
  testRunName?: string;
  jiraIssueKey: string;
  jiraIssueUrl: string;
  summary: string;
  status: string;
  createdAt: Date;
}

const route = useRoute();
const router = useRouter();
const projectId = computed(() => route.params.id as string);

const issues = ref<Issue[]>([]);
const loading = ref(false);
const error = ref('');
const searchQuery = ref('');
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const itemsPerPage = ref(20);
const hasAtlassianIntegration = ref(false);
const syncingWithJira = ref(false);
const syncMessage = ref('');

// Sorting state
const sortField = ref('createdAt');
const sortDirection = ref('desc');

// Filtering state
const showFilterModal = ref(false);
const filters = ref({
  status: [] as string[],
  testRunId: [] as string[],
  createdAfter: '',
  createdBefore: ''
});
// Temporary filters for the modal (to avoid applying until user clicks "Apply")
const tempFilters = ref({
  status: [] as string[],
  testRunId: [] as string[],
  createdAfter: '',
  createdBefore: ''
});

// Available filter options
const statusOptions = ref<string[]>([]);
const testRunOptions = ref<{id: string, name: string}[]>([]);

// Fetch issues for the current project
const fetchIssues = async () => {
  try {
    loading.value = true;
    error.value = '';

    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.value.toString(),
      sortField: sortField.value,
      sortDirection: sortDirection.value
    });

    // Add search parameter if provided
    if (searchQuery.value) {
      params.append('search', searchQuery.value);
    }

    // Add filter parameters if provided
    if (filters.value.status.length > 0) {
      filters.value.status.forEach(status => {
        params.append('status', status);
      });
    }

    if (filters.value.testRunId.length > 0) {
      filters.value.testRunId.forEach(id => {
        params.append('testRunId', id);
      });
    }

    if (filters.value.createdAfter) {
      params.append('createdAfter', filters.value.createdAfter);
    }

    if (filters.value.createdBefore) {
      params.append('createdBefore', filters.value.createdBefore);
    }

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId.value}/issues?${params.toString()}`
    );

    issues.value = response.data.issues;
    totalPages.value = response.data.totalPages;
    totalItems.value = response.data.total;

    // Extract unique status values for filter options
    const statuses = new Set<string>();
    issues.value.forEach(issue => {
      if (issue.status) {
        statuses.add(issue.status);
      }
    });
    statusOptions.value = Array.from(statuses);

    // Extract unique test runs for filter options
    const testRuns = new Map<string, string>();
    issues.value.forEach(issue => {
      if (issue.testRunId && issue.testRunName) {
        testRuns.set(issue.testRunId, issue.testRunName);
      }
    });
    testRunOptions.value = Array.from(testRuns.entries()).map(([id, name]) => ({ id, name }));

  } catch (err: any) {
    console.error('Failed to fetch issues:', err);
    error.value = err.response?.data?.message || 'Failed to fetch issues';
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchIssues();
};

// Handle sorting
const handleSort = (field: string) => {
  if (sortField.value === field) {
    // Toggle direction if clicking the same field
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Default to descending for a new sort field
    sortField.value = field;
    sortDirection.value = 'desc';
  }
  currentPage.value = 1;
  fetchIssues();
};

// Get sort icon based on current sort state
const getSortIcon = (field: string) => {
  if (sortField.value !== field) return '↕️';
  return sortDirection.value === 'asc' ? '↑' : '↓';
};

// Open filter modal
const openFilterModal = () => {
  // Copy current filters to temp filters
  tempFilters.value = {
    status: [...filters.value.status],
    testRunId: [...filters.value.testRunId],
    createdAfter: filters.value.createdAfter,
    createdBefore: filters.value.createdBefore
  };
  showFilterModal.value = true;
};

// Handle apply filters from modal component
const handleApplyFilters = (newFilters: typeof filters.value) => {
  filters.value = newFilters;
  currentPage.value = 1;
  fetchIssues();
};

// Reset and apply filters
const resetAndApplyFilters = () => {
  filters.value = {
    status: [],
    testRunId: [],
    createdAfter: '',
    createdBefore: ''
  };
  tempFilters.value = {
    status: [],
    testRunId: [],
    createdAfter: '',
    createdBefore: ''
  };
  currentPage.value = 1;
  fetchIssues();
};

// Get active filter count
const getActiveFilterCount = () => {
  let count = 0;
  if (filters.value.status.length > 0) count += filters.value.status.length;
  if (filters.value.testRunId.length > 0) count += filters.value.testRunId.length;
  if (filters.value.createdAfter) count++;
  if (filters.value.createdBefore) count++;
  return count;
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    fetchIssues();
  }
};

// Check if Atlassian integration is set up
const checkAtlassianIntegration = async () => {
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/integrations`);
    const integrations = response.data;
    hasAtlassianIntegration.value = integrations.some(
      (integration: any) => integration.type === 'atlassian' && integration.jiraUrl
    );
  } catch (error) {
    console.error('Failed to check Atlassian integration:', error);
    hasAtlassianIntegration.value = false;
  }
};

// Navigate to integration settings
const goToIntegrationSettings = () => {
  // Navigate to the root dashboard and emit an event to show integrations settings
  router.push('/').then(() => {
    // Use a custom event to communicate with the DashboardView component
    const event = new CustomEvent('show-settings', {
      detail: { setting: 'integrations' }
    });
    window.dispatchEvent(event);
  });
};

// Sync issues with JIRA
const syncWithJira = async () => {
  if (!hasAtlassianIntegration.value || syncingWithJira.value) {
    return;
  }

  try {
    syncingWithJira.value = true;
    syncMessage.value = '';
    error.value = '';

    // Call the backend endpoint to sync issues with JIRA
    const response = await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId.value}/issues/sync`
    );

    // Show success message
    syncMessage.value = response.data.message || 'Issues synchronized successfully with JIRA';

    // Refresh the issues list
    await fetchIssues();
  } catch (err: any) {
    console.error('Failed to sync with JIRA:', err);
    error.value = err.response?.data?.message || 'Failed to sync with JIRA';
  } finally {
    syncingWithJira.value = false;

    // Clear the sync message after 5 seconds
    setTimeout(() => {
      syncMessage.value = '';
    }, 5000);
  }
};

// Handle integration status change events
const handleIntegrationStatusChange = () => {
  checkAtlassianIntegration().then(() => {
    if (hasAtlassianIntegration.value) {
      fetchIssues();
    }
  });
};

onMounted(() => {
  checkAtlassianIntegration().then(() => {
    if (hasAtlassianIntegration.value) {
      fetchIssues();
    }
  });

  // Add event listener for integration status changes
  window.addEventListener('integration-status-changed', handleIntegrationStatusChange);
});

onUnmounted(() => {
  window.removeEventListener('integration-status-changed', handleIntegrationStatusChange);
});
</script>

<template>
  <div class="issues-page">
    <div class="page-header">
      <h1>Issues</h1>
      <div class="actions-container">
        <div class="search-container">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search issues..."
            @keyup.enter="handleSearch"
          />
          <button @click="handleSearch" class="search-button">Search</button>
        </div>

        <div class="buttons-container">
          <button
            @click="openFilterModal"
            class="filter-button"
            :class="{ active: getActiveFilterCount() > 0 }"
          >
            <span class="button-icon">🔍</span> Filter
            <span v-if="getActiveFilterCount() > 0" class="filter-badge">{{ getActiveFilterCount() }}</span>
          </button>

          <button
            @click="syncWithJira"
            class="sync-button"
            :disabled="syncingWithJira || !hasAtlassianIntegration"
            :title="hasAtlassianIntegration ? 'Sync issue statuses with JIRA' : 'Atlassian integration required'"
          >
            <span v-if="syncingWithJira" class="sync-spinner">↻</span>
            <span v-else>Sync with JIRA</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Filter Modal Component -->
    <FilterModal
      v-model="showFilterModal"
      :status-options="statusOptions"
      :test-run-options="testRunOptions"
      :initial-filters="tempFilters"
      @apply="handleApplyFilters"
    />

    <!-- Active Filters Component -->
    <ActiveFilters
      :filters="filters"
      :test-run-options="testRunOptions"
      @clear="resetAndApplyFilters"
    />

    <div v-if="!hasAtlassianIntegration" class="integration-required-message">
      <div class="message-icon">⚠️</div>
      <div class="message-content">
        <h3>Atlassian Integration Required</h3>
        <p>To use the Issues feature, you need to set up Atlassian integration first.</p>
        <p>This will allow you to create and track defects in JIRA directly from AgentQ.</p>
        <button @click="goToIntegrationSettings" class="setup-button">
          Set Up Atlassian Integration
        </button>
      </div>
    </div>

    <div v-if="syncMessage" class="success-message">
      <span class="success-icon">✓</span> {{ syncMessage }}
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-else-if="loading" class="loading-indicator">
      Loading issues...
    </div>

    <div v-else-if="issues.length === 0" class="no-data-message">
      No issues found. Issues are created when you report defects from test runs.
    </div>

    <div v-else class="issues-table-container">
      <table class="issues-table">
        <thead>
          <tr>
            <th @click="handleSort('defectId')" class="sortable">
              Defect ID <span class="sort-icon">{{ getSortIcon('defectId') }}</span>
            </th>
            <th @click="handleSort('jiraIssueKey')" class="sortable">
              JIRA Key <span class="sort-icon">{{ getSortIcon('jiraIssueKey') }}</span>
            </th>
            <th @click="handleSort('summary')" class="sortable">
              Summary <span class="sort-icon">{{ getSortIcon('summary') }}</span>
            </th>
            <th @click="handleSort('testRunName')" class="sortable">
              Test Run <span class="sort-icon">{{ getSortIcon('testRunName') }}</span>
            </th>
            <th @click="handleSort('status')" class="sortable">
              Status <span class="sort-icon">{{ getSortIcon('status') }}</span>
            </th>
            <th @click="handleSort('createdAt')" class="sortable">
              Created <span class="sort-icon">{{ getSortIcon('createdAt') }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="issue in issues" :key="issue.id">
            <td class="defect-id">D{{ issue.defectId }}</td>
            <td class="jira-key">
              <a v-if="issue.jiraIssueUrl" :href="issue.jiraIssueUrl" target="_blank">
                {{ issue.jiraIssueKey }}
              </a>
              <span v-else>-</span>
            </td>
            <td class="summary">{{ issue.summary }}</td>
            <td class="test-run">{{ issue.testRunName || 'Unknown' }}</td>
            <td class="status">
              <span
                class="status-badge"
                :class="'status-' + (issue.status?.toLowerCase().replace(/\s+/g, '') || 'unknown')"
              >
                {{ issue.status || 'Unknown' }}
              </span>
            </td>
            <td class="created-at">{{ formatDate(issue.createdAt.toString()) }}</td>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="pagination" v-if="totalPages > 1">
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-button"
        >
          Previous
        </button>
        <span class="page-info">Page {{ currentPage }} of {{ totalPages }}</span>
        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-button"
        >
          Next
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.issues-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h1 {
    margin: 0;
    font-size: 24px;
    color: #374151;
  }
}

.actions-container {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.search-container {
  display: flex;
  gap: 8px;

  input {
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    min-width: 250px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.buttons-container {
  display: flex;
  gap: 8px;
}

.filter-button {
  padding: 8px 16px;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  position: relative;

  &:hover {
    background-color: #e5e7eb;
  }

  &.active {
    background-color: #dbeafe;
    border-color: #93c5fd;
    color: #1e40af;
  }

  .button-icon {
    font-size: 14px;
  }

  .filter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #e94560;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
  }
}

  .search-button, .sync-button {
    padding: 8px 16px;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    margin-left: 8px;
  }

  .search-button {
    background-color: #e94560;

    &:hover {
      background-color: #d93a53; /* Manually darkened version of #e94560 */
    }
  }

  .sync-button {
    background-color: #3b82f6;
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) {
      background-color: #2563eb;
    }

    &:disabled {
      background-color: #93c5fd;
      cursor: not-allowed;
      opacity: 0.7;
    }

    .sync-spinner {
      display: inline-block;
      animation: spin 1s linear infinite;
      font-size: 16px;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  }

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;

  .success-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.loading-indicator {
  text-align: center;
  padding: 24px;
  color: #6b7280;
  font-size: 16px;
}

.integration-required-message {
  display: flex;
  gap: 20px;
  padding: 24px;
  background-color: #fff8e6;
  border: 1px solid #fde68a;
  border-radius: 8px;
  margin-bottom: 24px;

  .message-icon {
    font-size: 32px;
    line-height: 1;
  }

  .message-content {
    flex: 1;

    h3 {
      margin: 0 0 12px 0;
      color: #92400e;
      font-size: 18px;
    }

    p {
      margin: 0 0 8px 0;
      color: #78350f;
      font-size: 14px;
      line-height: 1.5;
    }

    .setup-button {
      margin-top: 16px;
      padding: 8px 16px;
      background-color: #f59e0b;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #d97706;
      }
    }
  }
}

.status-badge.small {
  padding: 2px 6px;
  font-size: 11px;
}

.no-data-message {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
  font-size: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px dashed #e5e7eb;
}

.issues-table-container {
  overflow-x: auto;
}

.issues-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;

    &.sortable {
      cursor: pointer;
      user-select: none;
      position: relative;
      padding-right: 24px;

      &:hover {
        background-color: #f3f4f6;
      }

      .sort-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
      }
    }
  }

  tr:hover {
    background-color: #f9fafb;
  }

  .defect-id {
    font-weight: 600;
    color: #e94560;
  }

  .jira-key {
    a {
      color: #2563eb;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .summary {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;

    /* AgentQ statuses */
    &.status-open {
      background-color: #dbeafe;
      color: #1e40af;
    }

    &.status-in-progress {
      background-color: #e0f2fe;
      color: #0369a1;
    }

    &.status-resolved {
      background-color: #dcfce7;
      color: #166534;
    }

    &.status-closed {
      background-color: #f3f4f6;
      color: #4b5563;
    }

    &.status-deleted {
      background-color: #fee2e2;
      color: #b91c1c;
    }

    /* JIRA statuses - lowercase and no spaces for CSS class names */
    &.status-todo, &.status-backlog {
      background-color: #dbeafe;
      color: #1e40af;
    }

    &.status-inprogress {
      background-color: #e0f2fe;
      color: #0369a1;
    }

    &.status-done, &.status-completed {
      background-color: #dcfce7;
      color: #166534;
    }

    &.status-onhold, &.status-blocked {
      background-color: #fef3c7;
      color: #92400e;
    }

    /* Default for any other status */
    &:not([class*="status-"]) {
      background-color: #f3f4f6;
      color: #4b5563;
    }
  }

  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    border: none;

    &.view-button {
      background-color: #e0f2fe;
      color: #0369a1;

      &:hover {
        background-color: #cce7f5; /* Manually darkened version of #e0f2fe */
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  gap: 16px;

  .pagination-button {
    padding: 8px 16px;
    background-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover:not(:disabled) {
      background-color: #e5e7eb;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-info {
    font-size: 14px;
    color: #6b7280;
  }
}
</style>
