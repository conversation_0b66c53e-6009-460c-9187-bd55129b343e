<script setup lang="ts">
import { ref } from 'vue';
import axios from 'axios';
import Completion from './Completion.vue';
import ReviewTestCases from './ReviewTestCases.vue';
import DataPreparation from './DataPreparation.vue';
import DataProcessing from './DataProcessing.vue';
import ConfirmationModal from '../../common/ConfirmationModal.vue';

interface ProcessedItem {
  id: string;
  type: 'file' | 'url';
}

const props = defineProps<{
  show: boolean;
}>();

const emit = defineEmits<{
  'close': [];
  'generate': [data: FormData];
}>();

const currentStep = ref(1);
const loading = ref(false);
const error = ref('');
const progress = ref(0);
const progressCheckInterval = ref<number | null>(null);
const processedItems = ref<ProcessedItem[]>([]);
const showConfirmationModal = ref(false);
const processingStatus = ref('');
const isProcessing = ref(false);

const validateApiKey = async (apiKey: string) => {
  try {
    const response = await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile/validate-api-key`, {
      apiKey,
    });
    return response.data;
  } catch (err) {
    console.error('API key validation failed:', err);
    return { valid: false };
  }
};

const checkEmbeddingProgress = async () => {
  try {
    const progressPromises = processedItems.value.map(async (item) => {
      const endpoint = item.type === 'url'
        ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/progress`
        : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/progress`;
      const response = await axios.get(endpoint);
      return response.data.progress;
    });

    const progressValues = await Promise.all(progressPromises);
    const averageProgress = progressValues.reduce((sum, val) => sum + val, 0) / progressValues.length;
    progress.value = averageProgress;

    if (progress.value === 100) {
      if (progressCheckInterval.value) {
        clearInterval(progressCheckInterval.value);
        progressCheckInterval.value = null;
      }
      setTimeout(() => {
        isProcessing.value = false;
        currentStep.value = 2;
      }, 500);
    }
  } catch (err) {
    console.error('Failed to check embedding progress:', err);
    if (progressCheckInterval.value) {
      clearInterval(progressCheckInterval.value);
      progressCheckInterval.value = null;
    }
  }
};

const startProgressTracking = () => {
  progress.value = 0;
  if (progressCheckInterval.value) {
    clearInterval(progressCheckInterval.value);
  }
  progressCheckInterval.value = setInterval(() => checkEmbeddingProgress(), 2000) as unknown as number;
};

const handleSubmit = async (data: {
  name: string;
  file: File[] | File | null;
  apiKey: string;
  publicUrl?: string;
  selectedIntegration?: string;
  privateUrl?: string;
  integrationType?: string;
  integrationEmail?: string;
  integrationApiToken?: string;
}) => {
  loading.value = true;
  isProcessing.value = true;
  error.value = '';
  processedItems.value = [];
  processingStatus.value = 'Validating API key...';
  progress.value = 0;

  try {
    const validationResult = await validateApiKey(data.apiKey);
    if (!validationResult.valid) {
      error.value = 'Invalid API key. Check your API Key then compare with API Key at https://agentq.id and try again.';
      loading.value = false;
      isProcessing.value = false;
      return;
    }

    if (validationResult.company?.subscription?.status === 'overlimit') {
      error.value = 'Your subscription is over the token limit. Please check your token usage at https://agentq.id.';
      loading.value = false;
      isProcessing.value = false;
      return;
    }

    // Process file uploads if present
    if (data.file && Array.isArray(data.file) && data.file.length > 0) {
      processingStatus.value = 'Uploading files...';
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('apiKey', data.apiKey);

      data.file.forEach((file) => {
        formData.append('file[]', file);
      });

      const response = await axios.post(
        `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload`,
        formData,
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const uploadProgress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
              progress.value = uploadProgress;
            }
          }
        }
      );

      if (Array.isArray(response.data)) {
        processedItems.value.push(...response.data.map(file => ({
          id: file.id,
          type: 'file' as const
        })));
      } else {
        processedItems.value.push({
          id: response.data.id,
          type: 'file'
        });
      }
    }

    // Process public URL if present
    if (data.publicUrl) {
      processingStatus.value = 'Processing public URL...';
      progress.value = 30; // Set initial progress for URL processing
      const response = await axios.post(`${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url`, {
        url_name: data.name,
        url_type: "public",
        url: data.publicUrl
      });
      processedItems.value.push({
        id: response.data.id,
        type: 'url'
      });
      progress.value = 60; // Update progress after URL processing
    }

    // Process private URL if present
    if (data.privateUrl && data.selectedIntegration) {
      processingStatus.value = 'Processing private URL...';
      progress.value = 60; // Set initial progress for private URL processing
      const response = await axios.post(`${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/private`, {
        url_name: data.name,
        url_type: "private",
        url: data.privateUrl,
        integration: data.integrationType,
        integration_email: data.integrationEmail,
        integration_api_token: data.integrationApiToken,
      });
      processedItems.value.push({
        id: response.data.id,
        type: 'url'
      });
      progress.value = 80; // Update progress after private URL processing
    }

    if (processedItems.value.length === 0) {
      throw new Error('No files or URLs were processed');
    }

    processingStatus.value = 'Generating embeddings...';
    startProgressTracking();

    // Monitor progress for all processed items
    const usageCheckInterval = setInterval(async () => {
      try {
        const progressPromises = processedItems.value.map(async (item) => {
          const progressEndpoint = item.type === 'url'
            ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/progress`
            : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/progress`;
          return axios.get(progressEndpoint);
        });

        const progressResponses = await Promise.all(progressPromises);
        const allCompleted = progressResponses.every(response => response.data.progress === 100);

        if (allCompleted) {
          clearInterval(usageCheckInterval);
          processingStatus.value = 'Processing usage data...';

          // Process usage data for all items
          for (const item of processedItems.value) {
            try {
              const usageEndpoint = item.type === 'url'
                ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/embedding-usage`
                : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/embedding-usage`;

              const embeddingUsageResponse = await axios.get(usageEndpoint);

              if (embeddingUsageResponse.status === 200) {
                const usageData = embeddingUsageResponse.data;
                await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile/usage-token`, {
                  tokenType: usageData.usageByType[0].tokenType,
                  tokenUsed: usageData.totalTokensUsed,
                  token: data.apiKey
                });
              }
            } catch (usageError) {
              console.error('Failed to process usage data:', usageError);
            }
          }
        }
      } catch (progressError) {
        console.error('Failed to check progress:', progressError);
        clearInterval(usageCheckInterval);
      }
    }, 2000);

  } catch (err: any) {
    console.error('Process failed:', err);
    error.value = err.message || 'Process failed. Please try again.';
    isProcessing.value = false;
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  if (currentStep.value >= 2) {
    showConfirmationModal.value = true;
  } else {
    resetForm();
    emit('close');
  }
};

const handleConfirmClose = async () => {
  if (processedItems.value.length > 0) {
    try {
      for (const item of processedItems.value) {
        const deleteEndpoint = item.type === 'url'
          ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}`
          : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}`;

        await axios.delete(deleteEndpoint);
      }
    } catch (err) {
      console.error('Error deleting items:', err);
    }
  }

  showConfirmationModal.value = false;
  resetForm();
  emit('close');
};

const handleCancelClose = () => {
  showConfirmationModal.value = false;
};

const resetForm = () => {
  currentStep.value = 1;
  error.value = '';
  progress.value = 0;
  processedItems.value = [];
  processingStatus.value = '';
  isProcessing.value = false;
  if (progressCheckInterval.value) {
    clearInterval(progressCheckInterval.value);
    progressCheckInterval.value = null;
  }
};

const handleStepBack = () => {
  currentStep.value = 1;
  processedItems.value = [];
};

const handleNextStep = () => {
  currentStep.value = 4;
};

const isStepCompleted = (step: number) => {
  return currentStep.value > step;
};
</script>

<template>
  <div v-if="show" class="modal">
    <div class="modal-content" :class="{ 'review-step-active': currentStep === 3 }">
      <button class="close-button" @click="handleClose">×</button>

      <div class="pipeline">
        <div class="pipeline-step-container">
          <span :class="['pipeline-step', { completed: isStepCompleted(1) || currentStep === 1 }]"></span>
          <span class="pipeline-note">Data Preparation</span>
        </div>
        <div class="pipeline-line"></div>
        <div class="pipeline-step-container">
          <span :class="['pipeline-step', { completed: isStepCompleted(2) || currentStep === 2 }]"></span>
          <span class="pipeline-note">Data Processing</span>
        </div>
        <div class="pipeline-line"></div>
        <div class="pipeline-step-container">
          <span :class="['pipeline-step', { completed: isStepCompleted(3) || currentStep === 3 }]"></span>
          <span class="pipeline-note">Reviewing Test Cases</span>
        </div>
        <div class="pipeline-line"></div>
        <div class="pipeline-step-container">
          <span :class="['pipeline-step', { completed: isStepCompleted(4) || currentStep === 4 }]"></span>
          <span class="pipeline-note">Completion</span>
        </div>
      </div>

      <DataPreparation
        v-if="currentStep === 1"
        :loading="loading"
        :progress="progress"
        :processing-status="processingStatus"
        :error="error"
        :is-processing="isProcessing"
        @submit="handleSubmit"
      />

      <DataProcessing
        v-if="currentStep === 2"
        :processedItems="processedItems"
        :fileIds="processedItems.map(item => item.id)"
        @step-back="handleStepBack"
        @next-step="currentStep = 3"
      />

      <ReviewTestCases
        v-if="currentStep === 3"
        :processedItems="processedItems"
        :fileIds="processedItems.map(item => item.id)"
        @step-back="currentStep = 2"
        @next-step="handleNextStep"
      />

      <Completion
        v-if="currentStep === 4"
        @completed="handleClose"
      />

      <!-- Processing Progress - Only shown when not in step 1 -->
      <div v-if="isProcessing && currentStep !== 1" class="processing-progress">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
        <div class="progress-status">{{ processingStatus }}</div>
        <div class="progress-percentage">{{ Math.round(progress) }}%</div>
      </div>
    </div>
  </div>

  <ConfirmationModal
    :is-open="showConfirmationModal"
    title="Confirmation Modal"
    :message="`Are you sure you want to close the modal? This will delete all processed data.`"
    @confirm="handleConfirmClose"
    @cancel="handleCancelClose"
  />
</template>

<style lang="scss" scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  padding: 32px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;

  &.review-step-active {
    max-width: 1400px;
    width: 95%;
  }
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;

  &:hover {
    color: #374151;
  }
}

.pipeline {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 32px;
}

.pipeline-step-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.pipeline-step {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #e5e7eb;

  &.completed {
    background-color: #e94560;
  }
}

.pipeline-line {
  width: 80px;
  height: 2px;
  background-color: #e5e7eb;
  margin: 0 8px;
}

.pipeline-note {
  font-size: 14px;
  color: #6b7280;
}

.processing-progress {
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
  }

  .progress-fill {
    height: 100%;
    background-color: #e94560;
    transition: width 0.3s ease;
  }

  .progress-status {
    text-align: center;
    color: #374151;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .progress-percentage {
    text-align: center;
    color: #6b7280;
    font-size: 12px;
  }
}
</style>