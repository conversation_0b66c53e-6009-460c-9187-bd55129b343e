import { defineStore } from 'pinia';
import axios from 'axios';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    refreshToken: localStorage.getItem('refreshToken') || '',
    tokenExpiry: localStorage.getItem('tokenExpiry') ? parseInt(localStorage.getItem('tokenExpiry') || '0') : 0,
    lastRefreshAttempt: 0, // Track when we last attempted to refresh
    refreshAttemptCount: 0 // Track how many consecutive refresh attempts
  }),
  
  getters: {
    isAuthenticated: (state) => !!state.token,
    getAuthHeader: (state) => `Bearer ${state.token}`
  },
  
  actions: {
    setToken(token: string, refreshToken: string = '', expiresIn: number = 3600) {
      this.token = token;
      this.refreshToken = refreshToken;
      
      // Calculate token expiry time (current time + expires_in in seconds)
      const expiryTime = Date.now() + (expiresIn * 1000);
      this.tokenExpiry = expiryTime;
      
      // Store in localStorage
      localStorage.setItem('token', token);
      if (refreshToken) {
        localStorage.setItem('refreshToken', refreshToken);
      }
      localStorage.setItem('tokenExpiry', expiryTime.toString());
    },
    
    setUser(user: any) {
      this.user = user;
      localStorage.setItem('user', JSON.stringify(user));
    },
    
    async login(email: string, password: string) {
      try {
        const response = await axios.post(`${(import.meta as any).env.VITE_BACKEND_URL}/auth/login`, {
          email,
          password
        });
        
        const { access_token, refresh_token, expires_in, user } = response.data;
        
        this.setToken(access_token, refresh_token || '', expires_in || 3600);
        this.setUser(user);
        
        return true;
      } catch (error) {
        return false;
      }
    },
    
    async setExternalToken(token: string) {
      // Set the token from external auth
      this.setToken(token, '', 3600); // Default 1 hour expiry if not specified
      
      // Fetch user data with the token
      try {
        const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/auth/profile`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        this.setUser(response.data);
      } catch (error) {
        console.error('Failed to fetch user profile', error);
      }
      
      return true;
    },
    
    async refreshAuthToken() {
      try {
        // Reset attempt count if it's been more than 1 minute since last attempt
        const now = Date.now();
        if (now - this.lastRefreshAttempt > 60000) {
          this.refreshAttemptCount = 0;
        }
        
        // Update last attempt time
        this.lastRefreshAttempt = now;
        
        // Increment attempt count
        this.refreshAttemptCount++;
        
        // If we've tried too many times in a short period, force logout
        if (this.refreshAttemptCount > 3) {
          console.error('Too many refresh attempts, logging out');
          this.logout();
          return false;
        }

        // Check if the backend URL is configured correctly
        const backendUrl = (import.meta as any).env.VITE_BACKEND_URL;
        if (!backendUrl) {
          console.error('Backend URL not configured');
          return false;
        }

        console.log('Attempting to refresh token with backend URL:', backendUrl);

        // Try to refresh with the current token first (without using refresh token)
        try {
          const response = await axios.post(`${backendUrl}/auth/token/refresh`, {}, {
            headers: { Authorization: `Bearer ${this.token}` }
          });
          
          const { access_token, refresh_token, expires_in } = response.data;
          this.setToken(access_token, refresh_token || this.refreshToken, expires_in || 3600);
          
          // Reset attempt count on success
          this.refreshAttemptCount = 0;
          return true;
        } catch (tokenError) {
          console.warn('Token refresh failed, trying with refresh token:', tokenError);
          
          // If that fails and we have a refresh token, try using it
          if (this.refreshToken) {
            try {
              const response = await axios.post(`${backendUrl}/auth/token/refresh`, {
                refresh_token: this.refreshToken
              });
              
              const { access_token, refresh_token, expires_in } = response.data;
              this.setToken(access_token, refresh_token || this.refreshToken, expires_in || 3600);
              
              // Reset attempt count on success
              this.refreshAttemptCount = 0;
              return true;
            } catch (refreshError) {
              console.error('Refresh token also failed:', refreshError);
              throw refreshError;
            }
          } else {
            throw tokenError;
          }
        }
      } catch (error) {
        console.error('Token refresh failed:', error);
        
        // If we get a 401 or 403 error, the refresh token is invalid, so logout
        if (axios.isAxiosError(error) && (error.response?.status === 401 || error.response?.status === 403)) {
          console.error('Authentication error during token refresh, logging out');
          this.logout();
        }
        
        return false;
      }
    },
    
    async checkTokenExpiry() {
      // If token is expired or about to expire (within 5 minutes), refresh it
      const fiveMinutesFromNow = Date.now() + (5 * 60 * 1000);
      
      // Only attempt refresh if:
      // 1. Token is actually expiring soon
      // 2. We haven't tried in the last 30 seconds (to prevent constant retries)
      if (this.tokenExpiry && 
          this.tokenExpiry < fiveMinutesFromNow && 
          Date.now() - this.lastRefreshAttempt > 30000) {
        console.log('Token is expiring soon, refreshing...');
        return this.refreshAuthToken();
      }
      
      return true;
    },
    
    async checkAuth() {
      try {
        // First check if token needs refreshing
        await this.checkTokenExpiry();
        
        // Then verify with the backend
        const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/auth/profile`, {
          headers: {
            'Authorization': this.getAuthHeader
          }
        });
        
        // Update user data
        this.setUser(response.data);
        
        return response.data;
      } catch (error) {
        console.error('Auth check failed:', error);
        // Check if error is 401 Unauthorized
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          // Clear auth data
          this.logout();
          // Redirect to login page
          window.location.href = '/login';
        }
        return null;
      }
    },
    
    logout() {
      this.token = '';
      this.user = null;
      this.refreshToken = '';
      this.tokenExpiry = 0;
      
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('tokenExpiry');
      localStorage.removeItem('companyData');
    }
  }
});
