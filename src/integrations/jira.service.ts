import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Integration } from './integration.entity';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { exec as execCallback } from 'child_process';

// Define a simplified File interface to avoid dependency on Express/Multer types
interface File {
  buffer: Buffer;
  originalname: string;
  mimetype: string;
  size: number;
}

@Injectable()
export class JiraService {
  private readonly logger = new Logger(JiraService.name);

  constructor(
    @InjectRepository(Integration)
    private integrationsRepository: Repository<Integration>,
    private configService: ConfigService,
  ) {}

  private async getJiraCredentials(): Promise<{ email: string; apiToken: string; jiraUrl: string } | null> {
    const integration = await this.integrationsRepository.findOne({
      where: { type: 'atlassian' }
    });

    if (!integration) {
      return null;
    }

    try {
      // Extract domain from JIRA URL
      const jiraUrl = integration.jiraUrl?.trim().replace(/\/$/, '');
      if (!jiraUrl) {
        throw new Error('JIRA URL not configured');
      }

      // Fetch cloud ID from tenant info
      const tenantResponse = await fetch(`${jiraUrl}/_edge/tenant_info`, {
        headers: {
          'Authorization': `Basic ${Buffer.from(`${integration.email}:${integration.apiToken}`).toString('base64')}`,
        }
      });

      if (!tenantResponse.ok) {
        throw new Error(`Failed to fetch tenant info: ${tenantResponse.status}`);
      }

      const tenantInfo = await tenantResponse.json();
      const cloudId = tenantInfo.cloudId;

      if (!cloudId) {
        throw new Error('Could not determine JIRA Cloud ID');
      }

      return {
        email: integration.email,
        apiToken: integration.apiToken,
        jiraUrl
      };
    } catch (error) {
      this.logger.error('Failed to get JIRA credentials:', error);
      return null;
    }
  }

  async getProjects(): Promise<any[]> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      return [];
    }

    try {
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/project`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      const projects = await response.json();
      return projects.map((project: any) => ({
        id: project.id,
        key: project.key,
        name: project.name
      }));
    } catch (error) {
      this.logger.error('Failed to fetch JIRA projects:', error);
      return [];
    }
  }

  async getPriorities(): Promise<any[]> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when fetching priorities');
      return [];
    }

    try {
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/priority`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      const priorities = await response.json();
      return priorities.map((priority: any) => ({
        id: priority.id,
        name: priority.name,
        iconUrl: priority.iconUrl
      }));
    } catch (error) {
      this.logger.error('Failed to fetch JIRA priorities:', error);
      return [];
    }
  }

  async getIssue(issueKey: string): Promise<any | null> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when fetching issue');
      return null;
    }

    try {
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/issue/${issueKey}`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        // If issue doesn't exist (404) or other error, return null
        if (response.status === 404) {
          this.logger.warn(`JIRA issue ${issueKey} not found`);
          return null;
        }

        const errorText = await response.text();
        this.logger.error(`JIRA API error (${response.status}): ${errorText}`);
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      this.logger.error(`Failed to fetch JIRA issue ${issueKey}:`, error);
      return null;
    }
  }

  async getIssueTypes(projectId: string): Promise<any[]> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when fetching issue types');
      return [];
    }

    try {
      // Use the createmeta endpoint which is more reliable for getting available issue types for a project
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/issue/createmeta?projectIds=${projectId}&expand=projects.issuetypes`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`JIRA API error (${response.status}): ${errorText}`);
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Extract issue types from the response
      if (data.projects && data.projects.length > 0) {
        const project = data.projects[0];
        if (project.issuetypes && project.issuetypes.length > 0) {
          return project.issuetypes.map((type: any) => ({
            id: type.id,
            name: type.name,
            iconUrl: type.iconUrl
          }));
        }
      }

      this.logger.warn(`No issue types found for project ID: ${projectId}`);
      return [];
    } catch (error) {
      this.logger.error(`Failed to fetch JIRA issue types for project ${projectId}:`, error);
      return [];
    }
  }

  async getProjectPriorities(projectId: string): Promise<any[]> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when fetching project priorities');
      return [];
    }

    try {
      // First, try to get project-specific priorities using the createmeta endpoint
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/issue/createmeta?projectIds=${projectId}&expand=projects.issuetypes.fields`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`JIRA API error (${response.status}): ${errorText}`);
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Extract priorities from the response
      if (data.projects && data.projects.length > 0) {
        const project = data.projects[0];
        if (project.issuetypes && project.issuetypes.length > 0) {
          // Get the first issue type (usually they all have the same priorities)
          const issueType = project.issuetypes[0];

          // Check if the priority field exists and has allowed values
          if (issueType.fields &&
              issueType.fields.priority &&
              issueType.fields.priority.allowedValues) {

            return issueType.fields.priority.allowedValues.map((priority: any) => ({
              id: priority.id,
              name: priority.name,
              iconUrl: priority.iconUrl
            }));
          }
        }
      }

      // If we couldn't get project-specific priorities, fall back to global priorities
      this.logger.warn(`No project-specific priorities found for project ID: ${projectId}, falling back to global priorities`);
      return this.getPriorities();
    } catch (error) {
      this.logger.error(`Failed to fetch JIRA priorities for project ${projectId}:`, error);
      // Fall back to global priorities on error
      return this.getPriorities();
    }
  }

  /**
   * Get allowed values for a specific custom field
   */
  async getCustomFieldValues(projectId: string, fieldId: string): Promise<any[]> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when fetching custom field values');
      return [];
    }

    return this.fetchAllowedValuesForField(credentials, projectId, fieldId);
  }

  /**
   * Fetch allowed values for a specific field in a project
   */
  private async fetchAllowedValuesForField(
    credentials: { email: string; apiToken: string; jiraUrl: string },
    projectId: string,
    fieldId: string
  ): Promise<any[]> {
    try {
      // First try to get the metadata for this field using the createmeta endpoint
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/issue/createmeta?projectIds=${projectId}&expand=projects.issuetypes.fields&fieldIds=${fieldId}`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        this.logger.error(`Failed to fetch allowed values for field ${fieldId}: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();

      // Extract allowed values from the response
      if (data.projects && data.projects.length > 0) {
        const project = data.projects[0];
        if (project.issuetypes && project.issuetypes.length > 0) {
          // Try each issue type until we find one with the field
          for (const issueType of project.issuetypes) {
            if (issueType.fields && issueType.fields[fieldId]) {
              const field = issueType.fields[fieldId];
              if (field.allowedValues && field.allowedValues.length > 0) {
                this.logger.log(`Found ${field.allowedValues.length} allowed values for field ${fieldId} in issue type ${issueType.name}`);
                return field.allowedValues;
              }
            }
          }
        }
      }

      // If we couldn't find allowed values, try another approach
      // For some custom fields, we might need to use a specific endpoint
      if (fieldId.startsWith('customfield_')) {
        try {
          // Try to get the field context
          const contextResponse = await fetch(
            `${credentials.jiraUrl}/rest/api/2/field/${fieldId}/context`,
            {
              headers: {
                'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
                'Accept': 'application/json'
              }
            }
          );

          if (contextResponse.ok) {
            const contextData = await contextResponse.json();
            if (contextData.values && contextData.values.length > 0) {
              const contextId = contextData.values[0].id;

              // Now try to get the options for this context
              const optionsResponse = await fetch(
                `${credentials.jiraUrl}/rest/api/2/field/${fieldId}/context/${contextId}/option`,
                {
                  headers: {
                    'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
                    'Accept': 'application/json'
                  }
                }
              );

              if (optionsResponse.ok) {
                const optionsData = await optionsResponse.json();
                if (optionsData.values && optionsData.values.length > 0) {
                  this.logger.log(`Found ${optionsData.values.length} options for field ${fieldId} using context API`);
                  return optionsData.values.map((option: any) => ({
                    id: option.id,
                    value: option.value
                  }));
                }
              }
            }
          }
        } catch (error) {
          this.logger.error(`Error fetching context options for field ${fieldId}:`, error);
        }
      }

      this.logger.warn(`Could not find allowed values for field ${fieldId}`);
      return [];
    } catch (error) {
      this.logger.error(`Error fetching allowed values for field ${fieldId}:`, error);
      return [];
    }
  }

  async getProjectCustomFields(projectId: string): Promise<any[]> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when fetching project custom fields');
      return [];
    }

    try {
      // First, try to get all fields to see what's available
      this.logger.log(`Fetching all available fields from Jira...`);
      const allFieldsResponse = await fetch(
        `${credentials.jiraUrl}/rest/api/2/field`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!allFieldsResponse.ok) {
        this.logger.error(`Failed to fetch all fields: ${allFieldsResponse.status} ${allFieldsResponse.statusText}`);
      } else {
        const allFields = await allFieldsResponse.json();
        this.logger.log(`Found ${allFields.length} total fields in Jira`);

        // Log all custom fields
        const allCustomFields = allFields.filter((field: any) => field.id.startsWith('customfield_'));
        this.logger.log(`All custom fields in Jira: ${allCustomFields.map((f: any) => `${f.id} (${f.name})`).join(', ')}`);
      }

      // Get project metadata including custom fields
      this.logger.log(`Fetching fields for project ${projectId}...`);
      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/issue/createmeta?projectIds=${projectId}&expand=projects.issuetypes.fields`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Accept': 'application/json'
          }
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`JIRA API error (${response.status}): ${errorText}`);
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const customFields: any[] = [];

      // Extract custom fields from the response
      if (data.projects && data.projects.length > 0) {
        const project = data.projects[0];
        if (project.issuetypes && project.issuetypes.length > 0) {
          // Get the first issue type
          const issueType = project.issuetypes[0];

          if (issueType.fields) {
            // Process each field
            Object.entries(issueType.fields).forEach(([fieldId, fieldData]: [string, any]) => {
              // Check if this is a field we want to include
              // Include custom fields (they start with "customfield_") and some standard fields that might be required
              const isCustomField = fieldId.startsWith('customfield_');
              const isEditableField = fieldData.schema && !fieldData.schema.readonly;

              // Log field details for debugging
              this.logger.log(`Field ${fieldId} (${fieldData.name}):
                - Type: ${fieldData.schema?.type || 'unknown'}
                - Required: ${fieldData.required || false}
                - Has allowed values: ${!!(fieldData.allowedValues && fieldData.allowedValues.length)}
                - Schema: ${JSON.stringify(fieldData.schema || {})}
              `);

              if (isCustomField && isEditableField) {
                customFields.push({
                  id: fieldId,
                  name: fieldData.name,
                  type: fieldData.schema?.type || 'unknown',
                  required: fieldData.required || false,
                  allowedValues: fieldData.allowedValues || [],
                  schema: fieldData.schema || {}
                });
              }
            });
          }
        }
      }

      this.logger.log(`Found ${customFields.length} custom fields for project ID: ${projectId}`);

      // Log all field IDs for debugging
      if (data.projects && data.projects.length > 0 &&
          data.projects[0].issuetypes && data.projects[0].issuetypes.length > 0 &&
          data.projects[0].issuetypes[0].fields) {
        this.logger.log(`All field IDs in response: ${Object.keys(data.projects[0].issuetypes[0].fields).join(', ')}`);
      }

      // Log custom fields that were found
      this.logger.log(`Custom fields found from createmeta: ${customFields.map(f => f.id).join(', ')}`);

      // Try to get a more complete list by combining with the fields endpoint
      try {
        const allFieldsResponse = await fetch(
          `${credentials.jiraUrl}/rest/api/2/field`,
          {
            headers: {
              'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
              'Accept': 'application/json'
            }
          }
        );

        if (allFieldsResponse.ok) {
          const allFields = await allFieldsResponse.json();
          const allCustomFields = allFields.filter((field: any) => field.id.startsWith('customfield_'));

          // Check for fields that are missing from our list
          for (const field of allCustomFields) {
            const isAlreadyIncluded = customFields.some(f => f.id === field.id);

            if (!isAlreadyIncluded) {
              this.logger.log(`Adding missing custom field: ${field.id} (${field.name})`);

              // Check if this field might be a dropdown or select field
              let isSelectField = field.schema?.type === 'option' ||
                                 field.schema?.type === 'array' ||
                                 (field.schema?.custom && (
                                   field.schema.custom.includes('select') ||
                                   field.schema.custom.includes('option') ||
                                   field.schema.custom.includes('dropdown')
                                 ));

              this.logger.log(`Field ${field.id} (${field.name}) schema: ${JSON.stringify(field.schema)}`);
              this.logger.log(`Is select field: ${isSelectField}`);

              // Add the field with default values
              const newField: any = {
                id: field.id,
                name: field.name,
                type: isSelectField ? 'option' : (field.schema?.type || 'string'),
                required: false,
                allowedValues: [],
                schema: field.schema || {},
                isSelectField: isSelectField
              };

              customFields.push(newField);
            }
          }
        }
      } catch (error) {
        this.logger.error('Error fetching additional field data:', error);
      }

      // Sort fields by name for better display
      customFields.sort((a, b) => a.name.localeCompare(b.name));

      this.logger.log(`Final custom fields list: ${customFields.map(f => f.id).join(', ')}`);
      return customFields;
    } catch (error) {
      this.logger.error(`Failed to fetch JIRA custom fields for project ${projectId}:`, error);
      return [];
    }
  }

  /**
   * Generate a proper Jira issue URL from the issue key and Jira URL
   */
  getJiraIssueUrl(jiraUrl: string, issueKey: string): string {
    // Ensure the Jira URL doesn't end with a slash
    const baseUrl = jiraUrl.endsWith('/') ? jiraUrl.slice(0, -1) : jiraUrl;
    return `${baseUrl}/browse/${issueKey}`;
  }

  /**
   * Upload attachments to a Jira issue
   * @param issueKey The key of the Jira issue
   * @param attachments Array of file buffers to upload
   * @returns True if successful, false otherwise
   */
  async uploadAttachments(issueKey: string, attachments: File[]): Promise<boolean> {
    if (!attachments || attachments.length === 0) {
      this.logger.log('No attachments to upload');
      return true;
    }

    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when uploading attachments');
      return false;
    }

    try {
      this.logger.log(`Uploading ${attachments.length} attachments to JIRA issue ${issueKey}`);

      // Process each attachment individually
      for (const file of attachments) {
        try {
          // Create a unique temporary file name to avoid conflicts
          const tempDir = os.tmpdir();
          const uniqueId = Date.now() + '-' + Math.random().toString(36).substring(2, 15);
          const safeFileName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
          const tempFilePath = path.join(tempDir, `${uniqueId}-${safeFileName}`);

          this.logger.log(`Creating temporary file at: ${tempFilePath}`);

          // Write the buffer to the temporary file
          fs.writeFileSync(tempFilePath, file.buffer);

          // Verify the file was created
          if (!fs.existsSync(tempFilePath)) {
            throw new Error(`Failed to create temporary file: ${tempFilePath}`);
          }

          // Use a Promise to handle the async curl execution
          await new Promise<void>((resolve, reject) => {
            // Escape the file path for the shell command
            const escapedFilePath = tempFilePath.replace(/(\s+)/g, '\\$1');

            // Build the curl command
            const curlCommand = `curl -X POST -u "${credentials.email}:${credentials.apiToken}" -H "X-Atlassian-Token: no-check" -F "file=@${escapedFilePath}" "${credentials.jiraUrl}/rest/api/2/issue/${issueKey}/attachments"`;

            // Execute the curl command
            execCallback(curlCommand, (error: Error | null, stdout: string, stderr: string) => {
              // Always try to clean up the temporary file first
              try {
                if (fs.existsSync(tempFilePath)) {
                  fs.unlinkSync(tempFilePath);
                  this.logger.log(`Deleted temporary file: ${tempFilePath}`);
                } else {
                  this.logger.warn(`Temporary file not found for cleanup: ${tempFilePath}`);
                }
              } catch (unlinkError) {
                this.logger.error(`Failed to delete temporary file ${tempFilePath}:`, unlinkError);
                // Continue with the upload result processing even if cleanup fails
              }

              // Now handle the upload result
              if (error) {
                this.logger.error(`Error uploading attachment ${file.originalname}: ${error.message}`);
                reject(error);
                return;
              }

              // Note: stderr from curl often contains progress information, not actual errors
              // Only log it as an error if it doesn't contain "% Total" which indicates progress output
              if (stderr && !stderr.includes('% Total')) {
                this.logger.error(`Error output when uploading attachment ${file.originalname}: ${stderr}`);
                reject(new Error(stderr));
                return;
              } else if (stderr) {
                // This is just progress information, log it as info
                this.logger.log(`Upload progress for ${file.originalname}: ${stderr.trim()}`);
              }

              this.logger.log(`Successfully uploaded attachment ${file.originalname} to JIRA issue ${issueKey}`);
              this.logger.log(`Response: ${stdout}`);
              this.logger.log(`Note: Attachment may take a moment to appear in JIRA UI`);

              resolve();
            });
          });
        } catch (fileError) {
          this.logger.error(`Failed to upload attachment ${file.originalname}:`, fileError);
          // Continue with the next file even if this one fails
        }
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to upload attachments to JIRA issue ${issueKey}:`, error);
      return false;
    }
  }

  async createIssue(
    projectId: string,
    issueTypeId: string,
    summary: string,
    description: string,
    priorityId: string,
    attachments?: File[],
    customFields?: { [key: string]: any }
  ): Promise<{ id: string; key: string; self: string; url: string } | null> {
    const credentials = await this.getJiraCredentials();
    if (!credentials) {
      this.logger.warn('No JIRA credentials found when creating issue');
      return null;
    }

    try {
      this.logger.log(`Creating JIRA issue in project ${projectId} with type ${issueTypeId}`);

      // Determine if projectId is numeric or a key
      const projectField = /^\d+$/.test(projectId)
        ? { id: projectId }
        : { key: projectId };

      // Prepare the issue data with required fields
      const fields: any = {
        project: projectField,
        summary: summary,
        description: description,
        issuetype: {
          id: issueTypeId
        },
        priority: {
          id: priorityId
        }
      };

      // Add custom fields if provided
      if (customFields) {
        this.logger.log(`Adding custom fields to Jira issue: ${JSON.stringify(customFields)}`);

        // Add each custom field directly to the fields object
        // The keys should already be the Jira field IDs (e.g., customfield_10011)
        Object.entries(customFields).forEach(([fieldId, value]) => {
          if (value !== null && value !== undefined && value !== '') {
            // Try to determine the field type and format accordingly
            if (typeof value === 'string' && value.match(/^\d+$/)) {
              // If it's a numeric string, it might be an ID for a select field
              fields[fieldId] = { id: value };
            } else if (Array.isArray(value)) {
              // Handle array values (multi-select fields)
              fields[fieldId] = value.map(item =>
                typeof item === 'object' ? item : { id: item }
              );
            } else {
              // For other types, use the value directly
              fields[fieldId] = value;
            }

            this.logger.log(`Added custom field ${fieldId} with value: ${JSON.stringify(fields[fieldId])}`);
          }
        });
      }

      const issueData = { fields };

      this.logger.log(`Issue data: ${JSON.stringify(issueData)}`);


      const response = await fetch(
        `${credentials.jiraUrl}/rest/api/2/issue`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${Buffer.from(`${credentials.email}:${credentials.apiToken}`).toString('base64')}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(issueData)
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`JIRA API error (${response.status}): ${errorText}`);
        throw new Error(`JIRA API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      this.logger.log(`Successfully created JIRA issue: ${result.key}`);

      // Generate the proper Jira issue URL
      const issueUrl = this.getJiraIssueUrl(credentials.jiraUrl, result.key);

      // Upload attachments if provided
      if (attachments && attachments.length > 0) {
        this.logger.log(`Uploading ${attachments.length} attachments to issue ${result.key}`);
        await this.uploadAttachments(result.key, attachments);
      }

      return {
        id: result.id,
        key: result.key,
        self: result.self,
        url: issueUrl
      };
    } catch (error) {
      this.logger.error(`Failed to create JIRA issue:`, error);
      return null;
    }
  }
}