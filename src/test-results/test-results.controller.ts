import { Controller, Post, Body, Param, Get, Delete, UseGuards, Query, HttpException, HttpStatus, NotFoundException } from '@nestjs/common';
import { TestRunsService } from '../test-runs/test-runs.service';
import { TestResultsService } from './test-results.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { StorageService } from './storage.service';

@ApiTags('test-results')
@Controller()
export class TestResultsController {
  constructor(
    private readonly testResultsService: TestResultsService, 
    private readonly StorageService: StorageService,
    private readonly testRunsService: TestRunsService,
  ) {}

  // Existing endpoints...

  @Post('projects/:projectId/test-runs/:testRunId/test-results/:resultId/logs')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Store logs for a test result' })
  @ApiResponse({ status: 201, description: 'Logs stored successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  async storeLogs(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('resultId') resultId: string,
    @Body() logsData: { projectId: string; tcId: string; logs: string[] }
  ) {
    try {
      const uploadData = {
        projectId: logsData.projectId,
        testCaseId: logsData.tcId,
        tcId: logsData.tcId,
        testRunId: testRunId,
        testResultId: resultId
      };
      const url = await this.StorageService.uploadLogs(uploadData, logsData.logs);
      
      // Update the test result with the logs URL
      await this.testResultsService.updateTestResultLogsUrl(resultId, url);
      
      return { success: true, url };
    } catch (error) {
      throw new HttpException(
        `Failed to store logs: ${error}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}