<template>
  <div v-if="hasActiveFilters" class="active-filters">
    <div class="active-filters-header">
      <h4>Active Filters:</h4>
      <button @click="clearAll" class="clear-all-button">Clear All</button>
    </div>

    <div class="filter-tags">
      <div v-for="(priority, index) in filters.priority" :key="`priority-${index}`" class="filter-tag">
        <span
          class="priority-badge small"
          :class="priority"
        >
          {{ capitalizeFirstLetter(priority) }}
        </span>
      </div>

      <div v-for="(platform, index) in filters.platform" :key="`platform-${index}`" class="filter-tag">
        Platform: {{ capitalizeFirstLetter(platform) }}
      </div>

      <div v-for="(testCaseType, index) in filters.testCaseType" :key="`testCaseType-${index}`" class="filter-tag">
        Test Case Type: {{ capitalizeFirstLetter(testCaseType) }}
      </div>

      <div v-for="(type, index) in filters.type" :key="`type-${index}`" class="filter-tag">
        Type: {{ capitalizeFirstLetter(type) }}
      </div>

      <div v-for="tagId in filters.tagIds" :key="`tag-${tagId}`" class="filter-tag">
        {{ getTagName(tagId) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Tag {
  id: string;
  name: string;
}

const props = defineProps<{
  filters: {
    priority: string[];
    platform: string[];
    testCaseType: string[];
    type: string[];
    tagIds: string[];
  };
  tagOptions: Tag[];
}>();

const emit = defineEmits<{
  (e: 'clear'): void;
}>();

// Check if there are any active filters
const hasActiveFilters = computed(() => {
  return (
    props.filters.priority.length > 0 ||
    props.filters.platform.length > 0 ||
    props.filters.testCaseType.length > 0 ||
    props.filters.type.length > 0 ||
    props.filters.tagIds.length > 0
  );
});

// Get tag name by ID
const getTagName = (id: string): string => {
  const tag = props.tagOptions.find(t => t.id === id);
  return tag?.name || 'Unknown Tag';
};

// Helper function to capitalize the first letter of a string
const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Clear all filters
const clearAll = () => {
  emit('clear');
};
</script>

<style lang="scss" scoped>
.active-filters {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;

  .active-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    h4 {
      margin: 0;
      font-size: 14px;
      color: #4b5563;
    }

    .clear-all-button {
      padding: 4px 8px;
      background-color: transparent;
      color: #e94560;
      border: 1px solid #e94560;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        background-color: #fef2f2;
      }
    }
  }

  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .filter-tag {
      background-color: white;
      border: 1px solid #e5e7eb;
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 12px;
      color: #4b5563;
      display: flex;
      align-items: center;
    }
  }
}

.priority-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;

  &.small {
    padding: 2px 6px;
    font-size: 11px;
  }

  &.low {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.medium {
    background-color: #fef3c7;
    color: #92400e;
  }

  &.high {
    background-color: #fee2e2;
    color: #991b1b;
  }

  &.critical {
    background-color: #7f1d1d;
    color: white;
  }
}
</style>
