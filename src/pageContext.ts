import { Page } from '@playwright/test';

export class PageContext {
  private static instance: PageContext;
  private page: Page | null = null;

  private constructor() {}

  static getInstance(): PageContext {
    if (!PageContext.instance) {
      PageContext.instance = new PageContext();
    }
    return PageContext.instance;
  }

  setPage(page: Page): void {
    this.page = page;
  }

  getPage(): Page {
    if (!this.page) {
      throw new Error('Page context not initialized. Make sure to call setPage() first.');
    }
    return this.page;
  }

  hasPage(): boolean {
    return this.page !== null;
  }

  clearPage(): void {
    this.page = null;
  }
}
