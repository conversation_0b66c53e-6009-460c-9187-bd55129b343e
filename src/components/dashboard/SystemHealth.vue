<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import axios from 'axios';

interface HealthData {
  status: string;
  timestamp: string;
  memory: {
    heapTotal: number;
    heapUsed: number;
    rss: number;
    memoryUsagePercent: number;
  };
  cpu: {
    system: number;
    user: number;
    percent: number;
    cpuCount: number;
  };
  uptime: number;
  database: {
    status: string;
    responseTime: number;
    activeConnections?: number;
    size?: {
      formatted: string;
      bytes: number;
    };
  };
}

const healthData = ref<HealthData | null>(null);
const loading = ref(false);
const error = ref('');
const refreshInterval = ref<number | null>(null);
const autoRefresh = ref(false);

const fetchHealthData = async (full = false) => {
  try {
    loading.value = true;
    error.value = '';
    
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/health/detailed${full ? '?full=true' : ''}`
    );
    
    healthData.value = response.data;
  } catch (err: any) {
    console.error('Failed to fetch health data:', err);
    error.value = err.response?.data?.message || 'Failed to fetch health data';
    healthData.value = null;
  } finally {
    loading.value = false;
  }
};

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value;
  
  if (autoRefresh.value) {
    // Refresh every 10 seconds
    refreshInterval.value = setInterval(() => {
      fetchHealthData();
    }, 10000) as unknown as number;
  } else if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
};

const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  
  if (days > 0) {
    return `${days}d ${hours}h ${minutes}m`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  } else {
    return `${remainingSeconds}s`;
  }
};

onMounted(() => {
  fetchHealthData();
});

onUnmounted(() => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
  }
});
</script>

<template>
  <div class="system-health">
    <div class="header">
      <h2>System Health</h2>
      <div class="actions">
        <button @click="fetchHealthData(true)" :disabled="loading" class="refresh-button">
          <span v-if="!loading">Refresh</span>
          <span v-else>Loading...</span>
        </button>
        <button @click="toggleAutoRefresh" class="auto-refresh-button" :class="{ active: autoRefresh }">
          {{ autoRefresh ? 'Auto-refresh: ON' : 'Auto-refresh: OFF' }}
        </button>
      </div>
    </div>
    
    <div v-if="error" class="error-message">{{ error }}</div>
    
    <div v-if="healthData" class="health-data">
      <div class="status-card" :class="{ 'status-ok': healthData.status === 'ok' }">
        <div class="status-indicator"></div>
        <div class="status-text">
          <h3>System Status: {{ healthData.status.toUpperCase() }}</h3>
          <p>Last updated: {{ new Date(healthData.timestamp).toLocaleString() }}</p>
        </div>
      </div>
      
      <div class="metrics-grid">
        <div class="metric-card">
          <h3>Memory</h3>
          <div class="metric-value">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${healthData.memory.memoryUsagePercent}%` }"
                :class="{ 
                  'warning': healthData.memory.memoryUsagePercent > 70,
                  'critical': healthData.memory.memoryUsagePercent > 85
                }"
              ></div>
            </div>
            <div class="progress-text">{{ healthData.memory.memoryUsagePercent }}%</div>
          </div>
          <div class="metric-details">
            <p>Heap Used: {{ healthData.memory.heapUsed }} MB</p>
            <p>Heap Total: {{ healthData.memory.heapTotal }} MB</p>
            <p>RSS: {{ healthData.memory.rss }} MB</p>
          </div>
        </div>
        
        <div class="metric-card">
          <h3>CPU</h3>
          <div class="metric-value">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${healthData.cpu.percent}%` }"
                :class="{ 
                  'warning': healthData.cpu.percent > 70,
                  'critical': healthData.cpu.percent > 85
                }"
              ></div>
            </div>
            <div class="progress-text">{{ healthData.cpu.percent }}%</div>
          </div>
          <div class="metric-details">
            <p>System CPU: {{ healthData.cpu.system }}ms</p>
            <p>User CPU: {{ healthData.cpu.user }}ms</p>
            <p>CPU Count: {{ healthData.cpu.cpuCount }}</p>
          </div>
        </div>
        
        <div class="metric-card">
          <h3>Database</h3>
          <div class="metric-value">
            <div class="status-badge" :class="{ 'status-ok': healthData.database.status === 'up' }">
              {{ healthData.database.status.toUpperCase() }}
            </div>
          </div>
          <div class="metric-details">
            <p>Response Time: {{ healthData.database.responseTime }}ms</p>
            <p v-if="healthData.database.activeConnections !== undefined">
              Active Connections: {{ healthData.database.activeConnections }}
            </p>
            <p v-if="healthData.database.size">
              Database Size: {{ healthData.database.size.formatted }}
            </p>
          </div>
        </div>
        
        <div class="metric-card">
          <h3>Uptime</h3>
          <div class="metric-value">
            <div class="uptime">{{ formatUptime(healthData.uptime) }}</div>
          </div>
          <div class="metric-details">
            <p>Started: {{ new Date(Date.now() - healthData.uptime * 1000).toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>
    
    <div v-else-if="!error && !loading" class="no-data">
      No health data available
    </div>
    
    <div v-if="loading" class="loading">
      Loading health data...
    </div>
  </div>
</template>

<style lang="scss" scoped>
.system-health {
  padding: 20px;
  background-color: #f9fafb;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    font-size: 20px;
    color: #374151;
    margin: 0;
  }
  
  .actions {
    display: flex;
    gap: 10px;
  }
}

button {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  
  &.refresh-button {
    background-color: #e94560;
    color: white;
    border: none;
    
    &:hover {
      background-color: darken(#e94560, 10%);
    }
    
    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }
  
  &.auto-refresh-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
    
    &:hover {
      background-color: #e5e7eb;
    }
    
    &.active {
      background-color: #10b981;
      color: white;
      border-color: #10b981;
      
      &:hover {
        background-color: darken(#10b981, 10%);
      }
    }
  }
}

.error-message {
  padding: 12px;
  background-color: #fee2e2;
  color: #b91c1c;
  border-radius: 6px;
  margin-bottom: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  .status-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #f87171;
    margin-right: 16px;
  }
  
  &.status-ok .status-indicator {
    background-color: #10b981;
  }
  
  .status-text {
    h3 {
      margin: 0 0 4px 0;
      font-size: 18px;
      color: #1f2937;
    }
    
    p {
      margin: 0;
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.metric-card {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: #374151;
  }
  
  .metric-value {
    margin-bottom: 16px;
  }
  
  .progress-bar {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
    
    .progress-fill {
      height: 100%;
      background-color: #10b981;
      transition: width 0.3s ease;
      
      &.warning {
        background-color: #f59e0b;
      }
      
      &.critical {
        background-color: #ef4444;
      }
    }
  }
  
  .progress-text {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    text-align: right;
  }
  
  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    background-color: #f87171;
    color: white;
    
    &.status-ok {
      background-color: #10b981;
    }
  }
  
  .uptime {
    font-size: 18px;
    font-weight: 500;
    color: #374151;
  }
  
  .metric-details {
    p {
      margin: 8px 0;
      font-size: 14px;
      color: #6b7280;
    }
  }
}

.no-data, .loading {
  padding: 40px;
  text-align: center;
  color: #6b7280;
  font-size: 16px;
}
</style>
