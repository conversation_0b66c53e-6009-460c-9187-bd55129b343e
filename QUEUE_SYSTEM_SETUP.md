# 🚀 BullMQ Queue System Setup

## Overview

This implementation adds a robust queue system using BullMQ to manage concurrent test executions. Only one test runs at a time while others wait in queue, preventing the "WebSocket is not connected" error when multiple users try to run tests simultaneously.

## 🎯 Features

- **Sequential Test Execution**: Only one test runs at a time
- **Queue Management**: Tests are queued and processed in FIFO order
- **Real-time Updates**: Users see their queue position and status
- **Dashboard Monitoring**: Web-based dashboard to monitor queue status
- **Automatic Cleanup**: Failed jobs and completed jobs are automatically cleaned up

## 🛠️ Installation

The required dependencies are already installed:
```bash
npm install bullmq @bull-board/api @bull-board/express ioredis
```

## 🔧 Configuration

### Environment Variables

Add to your `.env` file:
```env
# Redis Configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# BullMQ Dashboard
DASHBOARD_PORT=3020
```

### Redis Setup

Make sure Redis is running on your system:
```bash
# macOS with Homebrew
brew install redis
brew services start redis

# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# Windows
# Download and install Redis from https://redis.io/download
```

## 🚀 Running the Services

### Start All Services
```bash
# Backend API (port 3010)
cd app_backend_agentq
npm run start:dev

# Core Service (port 3000) - for API key validation
cd websocket_ai_automation_test_agentq
npm run core

# TestAutomation WebSocket with Queue (port 3008)
cd websocket_ai_automation_test_agentq
npm run dev:server1

# TestRun WebSocket (port 3009)
cd websocket_ai_automation_test_agentq
npm run dev:testrun

# Queue Dashboard (port 3020) - NEW
cd websocket_ai_automation_test_agentq
npm run dashboard

# Frontend (port 5174)
cd app_frontend_agentq
npm run dev
```

### Individual Services
```bash
# Queue Dashboard only
npm run dashboard

# Development with all services
npm run dev
```

## 📊 Queue Dashboard

Access the queue dashboard at: **http://localhost:3020**

### Dashboard Features:
- **Real-time Statistics**: View waiting, active, completed, and failed job counts
- **Queue Management**: Monitor and manage individual jobs
- **Job Details**: View job data, progress, and logs
- **API Endpoints**: RESTful API for queue operations

### API Endpoints:
- `GET /api/queue/stats` - Get queue statistics
- `GET /api/queue/job/:jobId` - Get job status
- `DELETE /api/queue/job/:jobId` - Cancel job
- `GET /health` - Health check

## 🔄 How It Works

### Queue Flow:
1. **User clicks "Run Test"** → Test is added to queue
2. **Queue Position Shown** → User sees their position in queue
3. **Test Execution** → When it's their turn, test starts running
4. **Real-time Updates** → User receives progress updates
5. **Completion** → Test completes and results are saved

### Frontend Updates:
- **Queue Status**: Shows "Queued..." when test is waiting
- **Position Indicator**: Displays queue position (e.g., "Position: 3")
- **Real-time Updates**: Status updates as queue progresses
- **Button States**: Run button is disabled when queued/running

## 🎛️ Queue Configuration

### Job Options (in QueueService):
```typescript
defaultJobOptions: {
  removeOnComplete: 10,    // Keep last 10 completed jobs
  removeOnFail: 50,        // Keep last 50 failed jobs
  attempts: 1,             // Don't retry failed tests
  backoff: {
    type: 'exponential',
    delay: 2000,
  },
}
```

### Worker Configuration:
```typescript
concurrency: 1  // Only process one test at a time
```

## 🔍 Monitoring

### Queue Statistics:
- **Waiting**: Number of tests in queue
- **Active**: Currently running tests (should be 0 or 1)
- **Completed**: Successfully completed tests
- **Failed**: Failed test executions

### Job States:
- `waiting` - In queue, waiting to be processed
- `active` - Currently being processed
- `completed` - Successfully finished
- `failed` - Failed during execution

## 🚨 Troubleshooting

### Common Issues:

1. **Redis Connection Error**:
   ```bash
   # Check if Redis is running
   redis-cli ping
   # Should return "PONG"
   ```

2. **Queue Not Processing**:
   - Check Redis connection
   - Verify worker is running
   - Check dashboard for stuck jobs

3. **Dashboard Not Loading**:
   - Verify port 3020 is not in use
   - Check console for errors
   - Ensure Redis is accessible

### Logs:
- Queue operations are logged to console
- Check WebSocket server logs for queue events
- Dashboard provides real-time job monitoring

## 🔧 Development

### Adding New Job Types:
1. Update `TestExecutionJob` interface in `queue-service.ts`
2. Add handling in `processTestJob` method
3. Update frontend to handle new job types

### Customizing Queue Behavior:
- Modify `QueueService` class for different queue strategies
- Update job options for different retry/cleanup policies
- Add custom job priorities if needed

## 📈 Performance

### Optimizations:
- Jobs are processed sequentially to prevent conflicts
- Automatic cleanup prevents memory leaks
- Redis provides fast, reliable queue storage
- Real-time updates minimize user confusion

### Scaling:
- Can be scaled horizontally by adding more worker processes
- Redis can be clustered for high availability
- Queue can handle thousands of jobs efficiently

## ✅ Testing

Test the queue system:
1. Open multiple browser tabs
2. Try to run tests simultaneously from different tabs
3. Observe queue behavior in dashboard
4. Verify only one test runs at a time
5. Check that all tests complete successfully
