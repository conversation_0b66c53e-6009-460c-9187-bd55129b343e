# JWT-Based Authentication for Test Automation

## Overview

Instead of using hardcoded email/password credentials, you can configure the test automation to use JWT_SECRET for authentication. This approach is more secure and eliminates the need for test user accounts.

## Benefits

✅ **No hardcoded credentials** - Uses JWT_SECRET from environment  
✅ **More secure** - No user passwords in test scripts  
✅ **Simpler setup** - No need to create/manage test users  
✅ **Service-level auth** - Uses system-level authentication  

## Implementation Options

### Option 1: JWT Token in Browser Storage (Recommended)

The updated setup script creates a test case that:

1. **Navigates to the application**
2. **Injects JWT token** into browser localStorage/sessionStorage
3. **Verifies authentication** without login form
4. **Tests protected routes** directly

### Option 2: API-Level Authentication

For API testing, you can use JWT_SECRET directly:

```javascript
// In test runner or automation steps
const jwt = require('jsonwebtoken');
const token = jwt.sign(
  { 
    sub: 'test-user',
    role: 'user',
    email: '<EMAIL>'
  }, 
  process.env.JWT_SECRET, 
  { expiresIn: '1h' }
);

// Use token for API calls
headers: {
  'Authorization': `Bearer ${token}`
}
```

### Option 3: Modified Test Runner

You can modify the test runner to automatically inject JWT tokens:

```javascript
// In test-runner.ts or master.spec.ts
if (process.env.USE_JWT_AUTH === 'true') {
  // Skip login form, inject JWT token directly
  await page.evaluate((token) => {
    localStorage.setItem('access_token', token);
    localStorage.setItem('user', JSON.stringify({
      email: '<EMAIL>',
      role: 'user'
    }));
  }, generatedJwtToken);
  
  // Navigate directly to dashboard
  await page.goto('http://localhost:5173/dashboard');
}
```

## Setup Instructions

### 1. Run the Updated Setup Script

```bash
node scripts/setup-test-case.js
```

This creates a JWT-based test case that:
- Uses JWT_SECRET for authentication
- Skips traditional login form
- Tests authentication at the token level

### 2. Environment Configuration

Make sure your `.env` file has:

```bash
JWT_SECRET=your_jwt_secret_key_change_in_production
USE_JWT_AUTH=true
```

### 3. Frontend Integration

Update your frontend auth store to accept JWT tokens:

```javascript
// In auth store or similar
export const useAuthStore = defineStore('auth', {
  actions: {
    setTokenFromJWT(jwtSecret) {
      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        { sub: 'test-user', role: 'user' },
        jwtSecret,
        { expiresIn: '1h' }
      );
      this.setToken(token);
    }
  }
});
```

## Test Case Configuration

The JWT-based test case includes these steps:

1. **Navigate to application** - Goes to the app URL
2. **Set JWT token** - Injects authentication token via JavaScript
3. **Navigate to protected area** - Tests dashboard or protected routes
4. **Verify access** - Confirms JWT authentication works

## AgentQ Integration

The test automation will:

1. **Use JWT_SECRET** from environment variables
2. **Generate valid tokens** for test scenarios
3. **Skip login forms** when JWT auth is enabled
4. **Test protected functionality** directly

## Security Considerations

✅ **JWT_SECRET in environment** - Never hardcode in scripts  
✅ **Test-only tokens** - Use short expiration times  
✅ **Separate test environment** - Don't use production secrets  
✅ **Token rotation** - Regenerate tokens for each test run  

## Troubleshooting

### Common Issues:

1. **JWT_SECRET not found**
   - Check `.env` file exists
   - Verify environment variable is set
   - Restart backend after changes

2. **Token validation fails**
   - Ensure JWT_SECRET matches between frontend/backend
   - Check token expiration time
   - Verify token format and claims

3. **Frontend doesn't recognize token**
   - Check localStorage/sessionStorage injection
   - Verify auth store integration
   - Test token validation endpoint

### Debug Commands:

```bash
# Test JWT token generation
node -e "
const jwt = require('jsonwebtoken');
const token = jwt.sign({sub: 'test'}, 'your_jwt_secret_key_change_in_production');
console.log('Token:', token);
"

# Verify backend accepts JWT_SECRET
curl -H "x-api-key: your_jwt_secret_key_change_in_production" http://localhost:3010/projects
```

## Next Steps

1. **Run the setup script** to create JWT-based test case
2. **Test the automation** from the frontend
3. **Verify no credentials needed** in test execution
4. **Extend to other test cases** as needed

This approach provides a more secure and maintainable way to handle authentication in your test automation system.
