<template>
    <div v-if="show" class="modal-overlay">
      <div class="modal-content">
        <div class="modal-header">
          <h3>Move Test Cases</h3>
          <button class="close-button" @click="$emit('close')">×</button>
        </div>
  
        <div class="modal-body">
          <p class="info-text">
            Select a folder to move {{ selectedCount }} test case{{ selectedCount > 1 ? 's' : '' }} to:
          </p>
  
          <div class="folder-list">
            <!-- <div class="folder-option">
              <label class="folder-label">
                <input
                  type="radio"
                  v-model="selectedFolderId"
                  :value="null"
                  name="folder"
                />
                <span>Root (No Folder)</span>
              </label>
            </div> -->
            
            <div 
              v-for="folder in flattenedFolders" 
              :key="folder.id" 
              class="folder-option"
            >
              <label class="folder-label">
                <input
                  type="radio"
                  v-model="selectedFolderId"
                  :value="folder.id"
                  name="folder"
                />
                <span>{{ '  '.repeat(folder.level) }}{{ folder.name }}</span>
              </label>
            </div>
          </div>
        </div>
  
        <div class="modal-actions">
          <button 
            type="button" 
            class="cancel-button"
            @click="$emit('close')"
          >
            Cancel
          </button>
          <button 
            type="button" 
            class="move-button"
            :disabled="loading"
            @click="handleMove"
          >
            {{ loading ? 'Moving...' : 'Move' }}
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue';
  
  const props = defineProps<{
    show: boolean;
    folders: any[];
    selectedCount: number;
    loading?: boolean;
  }>();
  
  const emit = defineEmits<{
    'close': [];
    'move': [folderId: string | null];
  }>();
  
  const selectedFolderId = ref<string | null>(null);
  
  const flattenedFolders = computed(() => {
    const flattened: any[] = [];
    
    const flatten = (folders: any[], level = 0) => {
      folders.forEach(folder => {
        flattened.push({
          ...folder,
          level
        });
        
        if (folder.children?.length) {
          flatten(folder.children, level + 1);
        }
      });
    };
    
    flatten(props.folders);
    return flattened;
  });
  
  const handleMove = () => {
    emit('move', selectedFolderId.value);
    selectedFolderId.value = null;
  };
  </script>
  
  <style lang="scss" scoped>
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }
  
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  
    h3 {
      margin: 0;
      font-size: 20px;
      color: #374151;
    }
  
    .close-button {
      background: none;
      border: none;
      font-size: 24px;
      color: #6b7280;
      cursor: pointer;
      padding: 4px;
      
      &:hover {
        color: #374151;
      }
    }
  }
  
  .info-text {
    color: #6b7280;
    margin-bottom: 16px;
  }
  
  .folder-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 8px;
  }
  
  .folder-option {
    margin: 8px 0;
  }
  
  .folder-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    
    &:hover {
      background-color: #f3f4f6;
    }
  
    input[type="radio"] {
      width: 16px;
      height: 16px;
    }
  
    span {
      font-size: 14px;
      color: #374151;
      white-space: pre;
    }
  }
  
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  
    button {
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
  
    .cancel-button {
      background-color: #f3f4f6;
      color: #374151;
      border: 1px solid #e5e7eb;
  
      &:hover {
        background-color: #e5e7eb;
      }
    }
  
    .move-button {
      background-color: #3b82f6;
      color: white;
      border: none;
  
      &:hover:not(:disabled) {
        background-color: #2563eb;
      }
  
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
  </style>