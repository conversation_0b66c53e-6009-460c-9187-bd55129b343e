<script setup lang="ts">
import { ref, defineEmits, onMounted, onUnmounted } from 'vue';

const emit = defineEmits(['close', 'export']);

const selectedFormat = ref<'pdf' | 'xml'>('pdf'); // Default to pdf

const handleExport = () => {
  emit('export', selectedFormat.value);
  emit('close');
};

const handleEsc = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('close');
  }
};

onUnmounted(() => {
  document.removeEventListener('keydown', handleEsc);
});

onMounted(() => {
    document.addEventListener('keydown', handleEsc);
  });
</script>

<template>
  <div class="modal">
    <div class="modal-content">
      <button class="close-button" @click="emit('close')">×</button>
      <h2>Export Test Run</h2>
      <div class="mt-4"><p>Select the desired format:</p></div>

      <div class="format-selection">
        <label>
          <input
            type="radio"
            value="pdf"
            v-model="selectedFormat"
            class="format-radio"
          />
          PDF
          <span class="material-icons">📄</span>
        </label>
      </div>

      <div class="form-actions">
        <button class="primary-button" @click.stop="handleExport">
          Export
        </button>
        <button class="cancel-button" @click="emit('close')">
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* You can copy the relevant modal styles from your main CSS  */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000; /* Ensure it's on top of other elements */
}

.modal-content {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Softer shadow */
  overflow: hidden; /* Prevent content overflow */
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
}

.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.primary-button {
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #d63553;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.cancel-button {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #e5e7eb;
  }
}
.format-selection {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.format-selection label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 16px;
  color: #374151;
  transition: color 0.2s;

  &:hover {
    color: #e94560;
  }
}

.format-radio {
  margin-right: 8px;
}

.material-icons {
  font-size: 1.2em;
  color: #6b7280;
}
</style>