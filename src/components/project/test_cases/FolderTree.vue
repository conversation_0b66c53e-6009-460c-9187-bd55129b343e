<template>
  <div><h2>Test Cases</h2></div>
   <div class="folder-tree">
      <div class="tree-header">
        <h3>Folder</h3>
        <button class="add-folder-button" @click="createRootFolder">
          + New Folder
        </button>
      </div>
  
      <div class="tree-content">
        <div v-for="item in folderStructure" :key="item.id" class="tree-item">
        <TreeItem
          :item="item"
          :level="0"
          @create-folder="createFolder"
          @delete-item="deleteItem"
          @move-item="moveItem"
          @select-item="$emit('select-item', $event)"
        />
      </div>
      </div>
  
      <!-- Create Folder Modal -->
      <div v-if="showCreateModal" class="modal">
        <div class="modal-content">
          <h3>Create New Folder</h3>
          <div class="form-group">
            <label>Folder Name</label>
            <input 
              v-model="newFolderName"
              type="text"
              placeholder="Enter folder name"
              @keyup.enter="handleCreateFolder"
            />
          </div>
          <div class="modal-actions">
            <button class="cancel-button" @click="cancelCreate">Cancel</button>
            <button 
              class="create-button" 
              @click="handleCreateFolder"
              :disabled="!newFolderName"
            >
              Create
            </button>
          </div>
        </div>
      </div>
  
      <!-- Delete Confirmation Modal -->
      <ConfirmationModal
        :is-open="showDeleteModal"
        title="Delete Folder"
        :message="deleteMessage"
        @confirm="handleDeleteConfirm"
        @cancel="handleDeleteCancel"
      />
    </div>
</template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import axios from 'axios';
  import TreeItem from '../test_cases/TreeItem.vue';
  import ConfirmationModal from '../../common/ConfirmationModal.vue';
  
  interface TreeItem {
    id: string;
    name: string;
    type: 'folder';
    children?: TreeItem[];
  }
  
  const props = defineProps<{
    selectedId?: string;
  }>();
  
  const emit = defineEmits<{
    'select-item': [item: TreeItem];
    'structure-updated': [];
  }>();
  
  const route = useRoute();
  const projectId = route.params.id as string;
  const structure = ref<TreeItem[]>([]);
  const showCreateModal = ref(false);
  const showDeleteModal = ref(false);
  const newFolderName = ref('');
  const currentParentId = ref<string | null>(null);
  const deletingItem = ref<TreeItem | null>(null);
  const deleteMessage = ref('');
  
  // Filter structure to only include folders
  const folderStructure = computed(() => {
    const filterFolders = (items: TreeItem[]): TreeItem[] => {
      return items
        .filter(item => item.type === 'folder')
        .map(folder => ({
          ...folder,
          children: folder.children ? filterFolders(folder.children) : []
        }));
    };
    
    return filterFolders(structure.value);
  });
  
  const fetchStructure = async () => {
    try {
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/structure`
      );
      structure.value = response.data;
    } catch (err) {
      console.error('Failed to fetch folder structure:', err);
    }
  };
  
  const createRootFolder = () => {
    currentParentId.value = null;
    showCreateModal.value = true;
  };
  
  const createFolder = (parentId: string) => {
    currentParentId.value = parentId;
    showCreateModal.value = true;
  };
  
  const handleCreateFolder = async () => {
    try {
      await axios.post(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/folders`,
        {
          name: newFolderName.value,
          parentId: currentParentId.value
        }
      );
      
      await fetchStructure();
      emit('structure-updated');
      
      showCreateModal.value = false;
      newFolderName.value = '';
      currentParentId.value = null;
      emit('structure-updated');
    } catch (err) {
      console.error('Failed to create folder:', err);
    }
  };
  
  const cancelCreate = () => {
    showCreateModal.value = false;
    newFolderName.value = '';
    currentParentId.value = null;
  };
  
  const deleteItem = (item: TreeItem) => {
    deletingItem.value = item;
    deleteMessage.value = `Are you sure you want to delete the folder "${item.name}" and all its contents?`;
    showDeleteModal.value = true;
  };
  
  const handleDeleteConfirm = async () => {
    if (!deletingItem.value) return;
  
    try {
      await axios.delete(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/folders/${deletingItem.value.id}`
      );
      
      await fetchStructure();
      emit('structure-updated');
    } catch (err) {
      console.error('Failed to delete folder:', err);
    } finally {
      showDeleteModal.value = false;
      deletingItem.value = null;
    }
  };
  
  const handleDeleteCancel = () => {
    showDeleteModal.value = false;
    deletingItem.value = null;
  };

  const isDragOverRoot = ref(false);

  const onRootDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }
};

const onRootDrop = (event: DragEvent) => {
  event.preventDefault();
  const draggedItemId = event.dataTransfer?.getData('text/plain');
  if (draggedItemId) {
    moveItem(draggedItemId, 'folder', null);
  }
};

const onRootDragLeave = () => {
  isDragOverRoot.value = false;
};

  
  const moveItem = async (itemId: string, itemType: 'folder', targetFolderId: string | null) => {
    try {
      await axios.post(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/move`,
        {
          itemId,
          itemType,
          targetFolderId
        }
      );
      
      await fetchStructure();
      emit('structure-updated');
    } catch (err) {
      console.error('Failed to move folder:', err);
    }
  };
  
  onMounted(() => {
    fetchStructure();
  });
</script>
  
  <style lang="scss" scoped>
  .folder-tree {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: 50%;
    display: flex;
    flex-direction: column;
    min-height: 300px;
  }
  
  .tree-header {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
  
    h3 {
      margin: 0;
      font-size: 16px;
      color: #374151;
    }
  }
  
  .add-folder-button {
    padding: 6px 12px;
    background-color: #8724c0;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
  
    &:hover {
      background-color: #5f108e;
    }
  }
  
  .tree-content {
    flex: 2;
    overflow-y: auto;
    padding: 10px;

    .root-drop-zone {
      padding: 8px;
      margin-bottom: 8px;
      border: 1px dashed #ccc;
      transition: all 0.2s ease;
    }

    .root-drop-zone.drag-active {
      border-color: #4CAF50; /* Green border when active */
      background-color: #f8fff8;
      box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
    }
  }
  
  .modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .modal-content {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 90%;
    max-width: 400px;
  
    h3 {
      margin: 0 0 16px;
      font-size: 18px;
      color: #374151;
    }
  }
  
  .form-group {
    margin-bottom: 20px;
  
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #374151;
    }
  
    input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      font-size: 14px;
  
      &:focus {
        outline: none;
        border-color: #e94560;
        box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
      }
    }
  }
  
  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  
    button {
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
    }
  
    .cancel-button {
      background-color: #f3f4f6;
      color: #374151;
      border: 1px solid #e5e7eb;
  
      &:hover {
        background-color: #e5e7eb;
      }
    }
  
    .create-button {
      background-color: #e94560;
      color: white;
      border: none;
  
      &:hover:not(:disabled) {
        background-color: #d63553;
      }
  
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
  </style>