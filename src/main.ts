import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { json, urlencoded } from 'body-parser';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { UsersService } from './users/user.service';
import * as express from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS for frontend
  app.enableCors({
    origin: ['http://localhost:5174', 'http://localhost:5173', 'http://localhost:4173', 'http://***********:3000', 'http://***********:3001', 'http://localhost:3010'], // Vite default port
    methods: ['GET', 'POST', 'PATCH', 'PUT', 'DELETE', 'OPTIONS'],
    credentials: true,
    allowedHeaders: 'Content-Type,Authorization',
  });

  // Enable validation
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true,
  }));

  // Add the body parser configuration
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ limit: '50mb', extended: true }));

  // Configure Express to handle multipart/form-data
  app.use(express.json());
  app.use(express.urlencoded({ extended: true }));

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('AgentQ API')
    .setDescription('The AgentQ API documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  // Seed default user
  const usersService = app.get(UsersService);
  try {
    const defaultUser = await usersService.findOne('<EMAIL>');
    if (!defaultUser) {
        console.log('Default user not found');
      // await usersService.create({
      //   email: '<EMAIL>',
      //   password: 'agentq',
      //   name: 'AgentQ User'
      // });
      // console.log('Default user created: <EMAIL> / agentq');
    }
  } catch (error) {
    console.error('Error seeding default user:', (error as Error).message);
  }

  await app.listen(3010);
  console.log('Application is running on: http://localhost:3010');
  console.log('Swagger documentation is available at: http://localhost:3010/api');
}
bootstrap();