import { config } from 'dotenv';
import { DashboardService } from './services/dashboard-service';

// Load environment variables
config();

console.log('🚀 Starting AgentQ Queue Dashboard...');

// Create and start the dashboard service
const dashboardService = new DashboardService();
const port = parseInt(process.env.DASHBOARD_PORT || '3020');

dashboardService.start(port);

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing dashboard server');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT signal received: closing dashboard server');
  process.exit(0);
});
