#!/usr/bin/env node

/**
 * Test script to verify WebSocket connection and AgentQ integration
 */

const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// Configuration
const WS_URL = process.env.WEBSOCKET_URL || 'ws://localhost:3008';
const API_KEY = process.env.AGENTQ_API_KEY || 'your_agentq_api_key_here';

console.log('🔧 Testing WebSocket connection to AgentQ automation server...');
console.log(`📡 WebSocket URL: ${WS_URL}`);
console.log(`🔑 API Key: ${API_KEY.substring(0, 8)}...`);

// Test WebSocket connection
function testWebSocketConnection() {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(WS_URL);
    let connected = false;

    const timeout = setTimeout(() => {
      if (!connected) {
        ws.terminate();
        reject(new Error('Connection timeout after 10 seconds'));
      }
    }, 10000);

    ws.on('open', () => {
      connected = true;
      clearTimeout(timeout);
      console.log('✅ WebSocket connection established');

      // Test authentication
      ws.send(JSON.stringify({
        type: 'auth',
        token: API_KEY
      }));
    });

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        console.log('📨 Received message:', message.type);

        if (message.type === 'auth_success') {
          console.log('✅ Authentication successful');
          
          // Add a delay before sending the test execution command
          setTimeout(() => {
            console.log('Sending test execution command...');
            ws.send(JSON.stringify({
              type: 'execute_test',
              token: API_KEY,
              testCaseId: '1',
              tcId: '1',
              steps: [/* your test steps */],
              testCase: {/* your test case */}
            }));
          }, 1000);
        }
      } catch (error) {
        console.error('❌ Failed to parse message:', error);
        reject(error);
      }
    });

    ws.on('error', (error) => {
      console.error('❌ WebSocket error:', error.message);
      reject(error);
    });

    ws.on('close', () => {
      console.log('🔌 WebSocket connection closed');
    });
  });
}

// Test AgentQ configuration
function testAgentQConfig() {
  console.log('\n🔧 Testing AgentQ configuration...');
  
  const configPath = path.join(process.cwd(), 'agentq.config.json');
  
  if (!fs.existsSync(configPath)) {
    console.log('⚠️  agentq.config.json not found, creating default...');
    
    const defaultConfig = {
      TOKEN: API_KEY,
      SERVICE_URL: "wss://websocket-ai-automation-test-api.agentq.id"
    };
    
    fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));
    console.log('✅ Created agentq.config.json');
  } else {
    console.log('✅ agentq.config.json exists');
    
    try {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
      if (config.TOKEN) {
        console.log('✅ AgentQ token configured');
      } else {
        console.log('⚠️  AgentQ token not set in config');
      }
    } catch (error) {
      console.error('❌ Invalid agentq.config.json format:', error.message);
    }
  }
}

// Test Playwright configuration
function testPlaywrightConfig() {
  console.log('\n🔧 Testing Playwright configuration...');
  
  const configPath = path.join(process.cwd(), 'playwright.config.ts');
  
  if (!fs.existsSync(configPath)) {
    console.log('⚠️  playwright.config.ts not found');
  } else {
    console.log('✅ playwright.config.ts exists');
  }
  
  // Check if tests directory exists
  const testsDir = path.join(process.cwd(), 'tests');
  if (!fs.existsSync(testsDir)) {
    console.log('📁 Creating tests directory...');
    fs.mkdirSync(testsDir, { recursive: true });
    console.log('✅ Tests directory created');
  } else {
    console.log('✅ Tests directory exists');
  }
}

// Main test function
async function runTests() {
  try {
    console.log('🚀 Starting AgentQ WebSocket integration tests...\n');
    
    // Test configurations
    testAgentQConfig();
    testPlaywrightConfig();
    
    // Test WebSocket connection
    console.log('\n🔧 Testing WebSocket connection...');
    await testWebSocketConnection();
    
    console.log('\n🎉 All tests passed! Your setup is ready for test execution.');
    console.log('\n📋 Next steps:');
    console.log('1. Start your backend server: npm run start');
    console.log('2. Start your frontend: npm run dev');
    console.log('3. Navigate to a test case and click "▶️ Run Test"');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the backend server is running on port 3008');
    console.log('2. Verify your AgentQ API key is correct');
    console.log('3. Check your network connection');
    console.log('4. Review the setup guide in TEST_EXECUTION_SETUP.md');
    process.exit(1);
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { testWebSocketConnection, testAgentQConfig, testPlaywrightConfig };
