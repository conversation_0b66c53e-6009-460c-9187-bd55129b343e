<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const showAnimation = ref(true);

onMounted(() => {
  // After animation completes, redirect back to test cases
  setTimeout(() => {
    router.go(0); // Refresh the page
  }, 3000);
});
</script>

<template>
  <div class="completion-container">
    <div class="completion-content">
      <div class="checkmark-circle">
        <div class="checkmark"></div>
      </div>
      <h2>Test Cases Processed Successfully!</h2>
      <p>Redirecting back to test cases...</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.completion-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.completion-content {
  text-align: center;
  
  h2 {
    margin: 24px 0 12px;
    color: #059669;
  }
  
  p {
    color: #6b7280;
  }
}

.checkmark-circle {
  width: 80px;
  height: 80px;
  position: relative;
  background: #dcfce7;
  border-radius: 50%;
  margin: 0 auto;
  animation: scale-up 0.3s ease-in-out;
}

.checkmark {
  width: 40px;
  height: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -60%) rotate(-45deg);
  border-left: 4px solid #059669;
  border-bottom: 4px solid #059669;
  animation: checkmark 0.4s ease-in-out 0.3s forwards;
  opacity: 0;
}

@keyframes scale-up {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: translate(-50%, -60%) rotate(-45deg) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -60%) rotate(-45deg) scale(1);
  }
}
</style>