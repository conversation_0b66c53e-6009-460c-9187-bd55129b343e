<template>
  <div v-if="hasActiveFilters" class="active-filters">
    <div class="active-filters-header">
      <h4>Active Filters:</h4>
      <button @click="clearAll" class="clear-all-button">Clear All</button>
    </div>
    
    <div class="filter-tags">
      <div v-for="(status, index) in filters.status" :key="`status-${index}`" class="filter-tag">
        <span 
          class="status-badge small" 
          :class="'status-' + status.toLowerCase().replace(/\s+/g, '')"
        >
          {{ status }}
        </span>
      </div>
      
      <div v-for="testRunId in filters.testRunId" :key="`testrun-${testRunId}`" class="filter-tag">
        {{ getTestRunName(testRunId) }}
      </div>
      
      <div v-if="filters.createdAfter" class="filter-tag">
        From: {{ formatDate(filters.createdAfter) }}
      </div>
      
      <div v-if="filters.createdBefore" class="filter-tag">
        To: {{ formatDate(filters.createdBefore) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface TestRun {
  id: string;
  name: string;
}

const props = defineProps<{
  filters: {
    status: string[];
    testRunId: string[];
    createdAfter: string;
    createdBefore: string;
  };
  testRunOptions: TestRun[];
}>();

const emit = defineEmits<{
  (e: 'clear'): void;
}>();

// Check if there are any active filters
const hasActiveFilters = computed(() => {
  return (
    props.filters.status.length > 0 ||
    props.filters.testRunId.length > 0 ||
    !!props.filters.createdAfter ||
    !!props.filters.createdBefore
  );
});

// Get test run name by ID
const getTestRunName = (id: string): string => {
  const testRun = props.testRunOptions.find(tr => tr.id === id);
  return testRun?.name || 'Unknown Test Run';
};

// Format date for display
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString();
};

// Clear all filters
const clearAll = () => {
  emit('clear');
};
</script>

<style lang="scss" scoped>
.active-filters {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 24px;
  
  .active-filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    h4 {
      margin: 0;
      font-size: 14px;
      color: #4b5563;
    }
    
    .clear-all-button {
      padding: 4px 8px;
      background-color: transparent;
      color: #e94560;
      border: 1px solid #e94560;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      
      &:hover {
        background-color: #fee2e2;
      }
    }
  }
  
  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    
    .filter-tag {
      background-color: white;
      border: 1px solid #e5e7eb;
      border-radius: 16px;
      padding: 4px 12px;
      font-size: 12px;
      color: #4b5563;
      display: flex;
      align-items: center;
    }
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
  margin: 0;

  &.small {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* AgentQ statuses */
  &.status-open {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.status-in-progress {
    background-color: #e0f2fe;
    color: #0369a1;
  }

  &.status-resolved {
    background-color: #dcfce7;
    color: #166534;
  }

  &.status-closed {
    background-color: #f3f4f6;
    color: #4b5563;
  }

  &.status-deleted {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  /* JIRA statuses - lowercase and no spaces for CSS class names */
  &.status-todo, &.status-backlog {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.status-inprogress {
    background-color: #e0f2fe;
    color: #0369a1;
  }

  &.status-done, &.status-completed {
    background-color: #dcfce7;
    color: #166534;
  }

  &.status-onhold, &.status-blocked {
    background-color: #fef3c7;
    color: #92400e;
  }

  /* Default for any other status */
  &:not([class*="status-"]) {
    background-color: #f3f4f6;
    color: #4b5563;
  }
}
</style>
