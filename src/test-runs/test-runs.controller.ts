import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query, UseInterceptors, UploadedFiles, UploadedFile, Headers, UnauthorizedException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiBody, ApiConsumes, ApiHeader } from '@nestjs/swagger';
import { FileFieldsInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { TestRunsService } from './test-runs.service';
import { CreateTestRunDto } from './dto/create-test-run.dto';
import { CreateTestResultDto } from '../test-results/dto/create-test-result.dto';
import { TestRun } from './test-run.entity';
import { TestResult } from '../test-results/test-result.entity';
import { TestResultHistory } from '../test-results/test-result-history.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { CreateIssueDto, IssueResponseDto } from '../test-results/dto/issues.dto';
import { JiraService } from '../integrations/jira.service';

@ApiTags('test-runs')
@ApiBearerAuth()
@Controller('projects/:projectId/test-runs')
@UseGuards(JwtAuthGuard)
export class TestRunsController {
  constructor(
    private readonly testRunsService: TestRunsService,
    private readonly jiraService: JiraService
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new test run' })
  @ApiResponse({ status: 201, description: 'Test run created successfully', type: TestRun })
  create(
    @Param('projectId') projectId: string,
    @Request() req,
    @Body() createTestRunDto: CreateTestRunDto,
  ) {
    return this.testRunsService.create(projectId, req.user.id, createTestRunDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all test runs for a project' })
  @ApiResponse({ status: 200, description: 'List of test runs', type: [TestRun] })
  findAll(
    @Param('projectId') projectId: string,
    @Request() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 50,
    @Query('search') search?: string
  ) {
    return this.testRunsService.findAll(projectId, req.user.id, {
      page: +page,
      limit: +limit,
      search
    });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific test run' })
  @ApiResponse({ status: 200, description: 'Test run found', type: TestRun })
  findOne(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.testRunsService.findOne(id, projectId, req.user.id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a test run' })
  @ApiResponse({ status: 200, description: 'Test run updated successfully', type: TestRun })
  update(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
    @Body() updateTestRunDto: Partial<CreateTestRunDto>,
  ) {
    return this.testRunsService.update(id, projectId, req.user.id, updateTestRunDto);
  }

  @Delete('batch')
  @ApiOperation({ summary: 'Delete multiple test runs in batch' })
  @ApiResponse({ status: 200, description: 'Test runs deleted successfully' })
  async removeBatch(
    @Param('projectId') projectId: string,
    @Body() data: { ids: string[] },
    @Request() req,
  ) {
    return this.testRunsService.removeBatch(data.ids, projectId, req.user.id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a test run' })
  @ApiResponse({ status: 200, description: 'Test run deleted successfully' })
  remove(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.testRunsService.remove(id, projectId, req.user.id);
  }

  @Post(':testRunId/test-results')
  @ApiOperation({ summary: 'Create a test result' })
  @ApiResponse({ status: 201, description: 'Test result created successfully', type: TestResult })
  @ApiHeader({
    name: 'x-api-key',
    description: 'API key for authentication (alternative to JWT)',
    required: false
  })
  createTestResult(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Request() req,
    @Body() createTestResultDto: CreateTestResultDto,
    @Headers('x-api-key') apiKey?: string,
  ) {
    // Check if using API key authentication
    if (apiKey) {
      return this.testRunsService.createTestResultWithApiKey(
        testRunId, 
        projectId, 
        apiKey, 
        createTestResultDto
      );
    }
    
    // Otherwise use JWT authentication
    return this.testRunsService.createTestResult(
      testRunId, 
      projectId, 
      req.user.id, 
      createTestResultDto
    );
  }

  @Get(':testRunId/test-results')
  @ApiOperation({ summary: 'Get paginated test results for a test run' })
  @ApiResponse({ status: 200, description: 'List of test results', type: [TestResult] })
  findAllTestResults(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Request() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('status') status?: string,
    @Query('search') search?: string,
    @Query('sortField') sortField?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
    @Query('statusFilter') statusFilter?: string[],
    @Query('priorityFilter') priorityFilter?: string[],
    @Query('tagFilter') tagFilter?: string[],
    @Query('typeFilter') typeFilter?: string[]
  ) {
    // Ensure page and limit are valid numbers
    const validPage = page && !isNaN(+page) ? +page : 1;
    const validLimit = limit && !isNaN(+limit) ? +limit : 20;

    return this.testRunsService.findAllTestResults(
      testRunId,
      projectId,
      req.user.id,
      {
        page: validPage,
        limit: validLimit,
        status,
        search,
        sortField,
        sortDirection: sortDirection || 'ASC',
        filters: {
          status: statusFilter,
          priority: priorityFilter,
          tagIds: tagFilter,
          type: typeFilter
        }
      }
    );
  }

  @Get(':testRunId/summary')
  @ApiOperation({ summary: 'Get test run summary statistics' })
  @ApiResponse({ status: 200, description: 'Summary statistics' })
  getTestRunSummary(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Request() req,
  ) {
    return this.testRunsService.getTestRunSummary(testRunId, projectId, req.user.id);
  }

  @Get('test-results/:id')
  @ApiOperation({ summary: 'Get a specific test result' })
  @ApiResponse({ status: 200, description: 'Test result found', type: TestResult })
  findOneTestResult(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.testRunsService.findOneTestResult(id, projectId, req.user.id);
  }

  @Patch('test-results/:id')
  @ApiOperation({ summary: 'Update a test result' })
  @ApiResponse({ status: 200, description: 'Test result updated successfully', type: TestResult })
  updateTestResult(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
    @Body() updateTestResultDto: Partial<CreateTestResultDto>,
  ) {
    return this.testRunsService.updateTestResult(id, projectId, req.user.id, updateTestResultDto);
  }

  @Post(':testRunId/test-results/bulk-update')
  @ApiOperation({ summary: 'Bulk update multiple test results' })
  @ApiResponse({ status: 200, description: 'The test results have been updated' })
  async bulkUpdateTestResults(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Request() req,
    @Body() bulkUpdateDto: { testResultIds: string[], status: string, actualResult?: string, notes?: string },
  ) {
    console.log('Received bulk update request:', {
      projectId,
      testRunId,
      testResultIds: bulkUpdateDto.testResultIds,
      status: bulkUpdateDto.status
    });

    return this.testRunsService.bulkUpdateTestResults(
      testRunId,
      projectId,
      req.user.id,
      bulkUpdateDto.testResultIds,
      {
        status: bulkUpdateDto.status as any, // Cast to any to avoid type issues
        actualResult: bulkUpdateDto.actualResult,
        notes: bulkUpdateDto.notes
      }
    );
  }

  @Patch(':testRunId/test-results/tcId/:tcId')
  @ApiOperation({ summary: 'Update a test result by test case ID within a specific test run' })
  @ApiResponse({ status: 200, description: 'Test result updated successfully', type: TestResult })
  @ApiBody({ type: CreateTestResultDto })
  async updateTestResultByTestCaseId(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('tcId') tcId: string,
    @Request() req,
    @Body() updateTestResultDto: Partial<CreateTestResultDto>,
  ) {
    return this.testRunsService.updateTestResultByTestCaseId(testRunId, tcId, projectId, req.user.id, updateTestResultDto);
  }

  @Delete('test-results/:id')
  @ApiOperation({ summary: 'Delete a test result' })
  @ApiResponse({ status: 200, description: 'Test result deleted successfully' })
  removeTestResult(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.testRunsService.removeTestResult(id, projectId, req.user.id);
  }

  @Get(':testRunId/test-results/:testResultId/history')
  @ApiOperation({ summary: 'Get history for a test result' })
  @ApiResponse({ status: 200, description: 'Test result history', type: [TestResultHistory] })
  getTestResultHistory(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('testResultId') testResultId: string,
    @Request() req,
  ) {
    return this.testRunsService.getTestResultHistory(testResultId, testRunId, projectId, req.user.id);
  }

  @Post(':testRunId/test-results/:testResultId/video')
  @ApiOperation({ summary: 'Upload video for a test result' })
  @ApiResponse({ status: 201, description: 'Video uploaded successfully' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadTestResultVideo(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('testResultId') testResultId: string,
    @Request() req,
    @UploadedFile() file: any,
  ) {
    console.log(`Uploading video for test result: ${testResultId}`);
    console.log(`File info:`, {
      originalname: file?.originalname,
      mimetype: file?.mimetype,
      size: file?.size
    });

    return this.testRunsService.uploadTestResultVideo(
      testResultId,
      testRunId,
      projectId,
      req.user.id,
      file
    );
  }

  @Post(':testRunId/test-results/:testResultId/screenshot')
  @ApiOperation({ summary: 'Upload screenshot for a test result' })
  @ApiResponse({ status: 201, description: 'Screenshot uploaded successfully' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('file'))
  async uploadTestResultScreenshot(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('testResultId') testResultId: string,
    @Request() req,
    @UploadedFile() file: any,
  ) {
    console.log(`Uploading screenshot for test result: ${testResultId}`);
    console.log(`File info:`, {
      originalname: file?.originalname,
      mimetype: file?.mimetype,
      size: file?.size
    });

    return this.testRunsService.uploadTestResultScreenshot(
      testResultId,
      testRunId,
      projectId,
      req.user.id,
      file
    );
  }

  @Get('storage/signed-url')
  @ApiOperation({ summary: 'Get signed URL for Google Cloud Storage file' })
  @ApiResponse({ status: 200, description: 'Signed URL generated successfully' })
  async getSignedUrl(
    @Query('gcsUrl') gcsUrl: string,
  ) {
    if (!gcsUrl) {
      throw new Error('GCS URL is required');
    }

    const signedUrl = await this.testRunsService.getSignedUrl(gcsUrl);
    return { signedUrl };
  }

  @Post(':testRunId/test-results/:testResultId/jira-issue')
  @ApiOperation({ summary: 'Create a JIRA issue for a test result' })
  @ApiResponse({ status: 201, description: 'JIRA issue created successfully', type: IssueResponseDto })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileFieldsInterceptor([
    { name: 'attachments', maxCount: 10 },
  ]))
  async createJiraIssue(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('testResultId') testResultId: string,
    @Request() req,
    @Body() formData: any,
    @UploadedFiles() files: { attachments?: any[] },
  ) {
    // Log attachment information
    console.log('Attachments received:', files?.attachments?.length || 0);
    // Log the form data for debugging
    console.log('Raw form data received:', formData);
    console.log('Project ID:', formData.projectId);
    console.log('Issue Type:', formData.issueType);
    console.log('Priority:', formData.priority);

    // Check if we have the required fields
    if (!formData.projectId || !formData.issueType || !formData.priority) {
      console.error('Missing required fields in form data');
      console.log('Request body:', req.body);

      // Try to extract from raw request body if available
      const rawBody = req.body;
      if (rawBody) {
        formData.projectId = rawBody.projectId || formData.projectId;
        formData.issueType = rawBody.issueType || formData.issueType;
        formData.priority = rawBody.priority || formData.priority;
        formData.summary = rawBody.summary || formData.summary;
        formData.description = rawBody.description || formData.description;

        console.log('Updated form data:', {
          projectId: formData.projectId,
          issueType: formData.issueType,
          priority: formData.priority,
          summary: formData.summary,
          description: formData.description
        });
      }
    }

    // Extract form data directly from the request if needed
    const projectIdValue = formData.projectId || req.body?.projectId;
    const issueTypeValue = formData.issueType || req.body?.issueType;
    const priorityValue = formData.priority || req.body?.priority;
    const summaryValue = formData.summary || req.body?.summary || '';
    const descriptionValue = formData.description || req.body?.description || '';

    // Extract custom fields
    const customFields: { [key: string]: any } = {};

    // Check if customFields exists in the form data
    if (formData.customFields || req.body?.customFields) {
      const customFieldsData = formData.customFields || req.body?.customFields;

      // If it's a string (JSON), parse it
      if (typeof customFieldsData === 'string') {
        try {
          Object.assign(customFields, JSON.parse(customFieldsData));
        } catch (e) {
          console.error('Error parsing custom fields JSON:', e);
        }
      }
      // If it's already an object, use it directly
      else if (typeof customFieldsData === 'object') {
        Object.assign(customFields, customFieldsData);
      }
    }

    // Check for individual custom field entries (customFields[fieldId])
    for (const key in formData) {
      if (key.startsWith('customFields[') && key.endsWith(']')) {
        // Extract the field ID from the key
        const fieldId = key.substring(13, key.length - 1);
        customFields[fieldId] = formData[key];
      }
    }

    // Also check in req.body
    if (req.body) {
      for (const key in req.body) {
        if (key.startsWith('customFields[') && key.endsWith(']')) {
          // Extract the field ID from the key
          const fieldId = key.substring(13, key.length - 1);
          customFields[fieldId] = req.body[key];
        }
      }
    }

    console.log('Final values for Jira issue creation:');
    console.log('Project ID:', projectIdValue);
    console.log('Issue Type:', issueTypeValue);
    console.log('Priority:', priorityValue);
    console.log('Summary:', summaryValue);
    console.log('Custom Fields:', customFields);
    console.log('Description:', descriptionValue);

    // First, create the issue in Jira
    const jiraIssue = await this.jiraService.createIssue(
      projectIdValue,
      issueTypeValue,
      summaryValue,
      descriptionValue,
      priorityValue,
      files?.attachments,
      Object.keys(customFields).length > 0 ? customFields : undefined
    );

    // If Jira issue creation was successful, save it to our database
    if (jiraIssue) {
      console.log('Jira issue created successfully:', jiraIssue);

      // Create a proper DTO object with the Jira issue details
      const createIssueDto: CreateIssueDto = {
        testResultId: testResultId,
        summary: summaryValue,
        description: descriptionValue,
        jiraIssueId: jiraIssue.id,
        jiraIssueKey: jiraIssue.key,
        jiraIssueUrl: jiraIssue.url || null,
        status: 'To Do'
      };

      return this.testRunsService.createJiraIssue(testResultId, testRunId, projectId, req.user.id, createIssueDto);
    } else {
      // If Jira issue creation failed, still create a record in our database
      console.log('Failed to create Jira issue, creating local record only');

      const createIssueDto: CreateIssueDto = {
        testResultId: testResultId,
        summary: summaryValue,
        description: descriptionValue,
        status: 'To Do'
      };

      return this.testRunsService.createJiraIssue(testResultId, testRunId, projectId, req.user.id, createIssueDto);
    }
  }

  @Get(':testRunId/test-results/:testResultId/jira-issue')
  @ApiOperation({ summary: 'Get JIRA issue for a test result' })
  @ApiResponse({ status: 200, description: 'JIRA issue found', type: IssueResponseDto })
  async getJiraIssue(
    @Param('projectId') projectId: string,
    @Param('testRunId') testRunId: string,
    @Param('testResultId') testResultId: string,
    @Request() req,
  ) {
    return this.testRunsService.getJiraIssue(testResultId, testRunId, projectId, req.user.id);
  }

  @Get('/issues')
  @ApiOperation({ summary: 'Get all issues for a project' })
  @ApiResponse({ status: 200, description: 'List of issues', type: [IssueResponseDto] })
  async getProjectIssues(
    @Param('projectId') projectId: string,
    @Request() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('search') search?: string
  ) {
    return this.testRunsService.getProjectIssues(projectId, req.user.id, {
      page: +page,
      limit: +limit,
      search
    });
  }
}
