<template>
  <div class="settings-page">
    <h2>Integrations</h2>
    <p class="settings-description">Manage your third-party integrations.</p>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-if="success" class="success-message">
      {{ success }}
    </div>

    <!-- Add Integration Form -->
    <div class="settings-card">
      <h3>Add Atlassian Integration</h3>
      <p>Connect your Atlassian account to enable JIRA and Confluence integration.</p>

      <form @submit.prevent="handleSubmit" class="integration-form">
        <div class="form-group">
          <label>Email</label>
          <input
            v-model="email"
            type="email"
            required
            placeholder="Enter your Atlassian email"
          />
        </div>

        <div class="form-group">
          <label>JIRA URL/Domain</label>
          <input
            v-model="jiraUrl"
            type="url"
            required
            placeholder="https://your-site.atlassian.net"
          />
        </div>

        <div class="form-group">
          <label>API Token</label>
          <input
            v-model="apiToken"
            type="password"
            required
            placeholder="Enter your Atlassian API token"
          />
          <p class="help-text">
            You can generate an API token from your
            <a
              href="https://id.atlassian.com/manage-profile/security/api-tokens"
              target="_blank"
              rel="noopener noreferrer"
            >
              Atlassian Account Settings
            </a>
          </p>
        </div>

        <button type="submit" class="submit-button" :disabled="loading">
          {{ loading ? 'Adding...' : 'Add Integration' }}
        </button>
      </form>
    </div>

    <!-- Existing Integrations -->
    <div class="settings-card">
      <h3>Active Integrations</h3>

      <div v-if="loading" class="loading-state">
        Loading integrations...
      </div>

      <div v-else-if="integrations.length === 0" class="empty-state">
        No integrations found.
      </div>

      <div v-else class="integrations-list">
        <div
          v-for="integration in integrations"
          :key="integration.id"
          class="integration-item"
        >
          <div class="integration-info">
            <div class="integration-type">
              {{ integration.type }}
            </div>
            <div class="integration-email">
              {{ integration.email }}
            </div>
            <div class="integration-url" v-if="integration.jiraUrl">
              {{ integration.jiraUrl }}
            </div>
            <div class="integration-date">
              Added on {{ new Date(integration.createdAt).toLocaleDateString() }}
            </div>
          </div>

          <button
            class="remove-button"
            @click="showRemoveConfirmation(integration.id)"
            :disabled="loading"
          >
            Remove
          </button>
        </div>
      </div>
    </div>

    <!-- Confirmation Modal -->
    <div v-if="showConfirmModal" class="modal-overlay">
      <div class="confirmation-modal">
        <div class="modal-header">
          <h3>Confirm Removal</h3>
          <button class="close-button" @click="closeConfirmModal">×</button>
        </div>
        <div class="modal-body">
          <p class="warning-text">
            Are you sure you want to remove this integration? This action cannot be undone.
          </p>
          <p class="warning-text">
            All connections to JIRA will be lost, and you'll need to set up the integration again to restore functionality.
          </p>
          <div class="confirmation-input">
            <label>Type <strong>REMOVE</strong> to confirm:</label>
            <input
              v-model="confirmationText"
              type="text"
              placeholder="REMOVE"
              :class="{ 'error': error && confirmationText !== 'REMOVE' }"
            />
          </div>
          <div v-if="error && confirmationText !== 'REMOVE'" class="input-error">
            {{ error }}
          </div>
        </div>
        <div class="modal-footer">
          <button class="cancel-button" @click="closeConfirmModal">Cancel</button>
          <button
            class="confirm-button"
            @click="confirmRemove"
            :disabled="confirmationText !== 'REMOVE'"
          >
            Confirm
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useAuthStore } from '../../stores/auth';

interface Integration {
  id: string;
  type: string;
  email: string;
  apiToken: string;
  jiraUrl: string;
  companyId: string;
  createdAt: string;
  updatedAt: string;
}

const integrations = ref<Integration[]>([]);
const loading = ref(false);
const error = ref('');
const success = ref('');

// Confirmation modal state
const showConfirmModal = ref(false);
const integrationToRemove = ref<string | null>(null);
const confirmationText = ref('');

// Form data
const email = ref('');
const apiToken = ref('');
const jiraUrl = ref('');

const fetchIntegrations = async () => {
  try {
    loading.value = true;
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/integrations`);
    integrations.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch integrations';
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async () => {
  try {
    loading.value = true;
    error.value = '';
    success.value = '';

    // Get the auth store
    const authStore = useAuthStore();
    
    // Get company ID from auth store user data
    let companyId = authStore.user?.companyId;
    
    // If not found in user object, try to refresh auth data
    if (!companyId) {
      try {
        const userData = await authStore.checkAuth();
        companyId = userData?.companyId;
      } catch (authErr) {
        console.error('Failed to refresh auth data:', authErr);
      }
    }
    
    if (!companyId) {
      error.value = 'Company ID not found. Please refresh the page or log in again.';
      return;
    }

    const payload = {
      type: 'atlassian',
      email: email.value,
      apiToken: apiToken.value,
      jiraUrl: jiraUrl.value,
      companyId: companyId
    };

    await axios.post(`${(import.meta as any).env.VITE_BACKEND_URL}/integrations`, payload);

    success.value = 'Integration added successfully';
    await fetchIntegrations();

    // Reset form
    email.value = '';
    apiToken.value = '';
    jiraUrl.value = '';

    // Dispatch a custom event to notify other components
    const event = new CustomEvent('integration-status-changed', {
      detail: { type: 'added', integrationType: 'atlassian' }
    });
    window.dispatchEvent(event);
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to add integration';
  } finally {
    loading.value = false;
  }
};

// Show confirmation modal before removing
const showRemoveConfirmation = (id: string) => {
  integrationToRemove.value = id;
  confirmationText.value = '';
  showConfirmModal.value = true;
};

// Close the confirmation modal
const closeConfirmModal = () => {
  showConfirmModal.value = false;
  integrationToRemove.value = null;
  confirmationText.value = '';
};

// Confirm removal of integration
const confirmRemove = async () => {
  if (confirmationText.value !== 'REMOVE') {
    error.value = 'Please type "REMOVE" to confirm deletion';
    return;
  }

  if (!integrationToRemove.value) return;

  try {
    loading.value = true;
    error.value = '';
    success.value = '';

    await axios.delete(`${(import.meta as any).env.VITE_BACKEND_URL}/integrations/${integrationToRemove.value}`);
    success.value = 'Integration removed successfully';
    await fetchIntegrations();

    // Dispatch a custom event to notify other components
    const event = new CustomEvent('integration-status-changed', {
      detail: { type: 'removed', integrationType: 'atlassian' }
    });
    window.dispatchEvent(event);

    // Close the modal
    closeConfirmModal();
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to remove integration';
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchIntegrations();
  const authStore = useAuthStore();
  authStore.checkAuth();
});
</script>

<style lang="scss" scoped>
.settings-page {
  h2 {
    margin-bottom: 8px;
    font-size: 24px;
  }

  .settings-description {
    color: #6b7280;
    margin-bottom: 24px;
  }
}

.settings-card {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin-bottom: 12px;
    font-size: 18px;
    color: #374151;
  }

  p {
    margin-bottom: 24px;
    color: #6b7280;
  }
}

.integration-form {
  max-width: 500px;
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }

  input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }

  .help-text {
    margin-top: 4px;
    font-size: 14px;
    color: #6b7280;

    a {
      color: #e94560;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.submit-button {
  padding: 8px 16px;
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;

  &:hover:not(:disabled) {
    background-color: #d63553;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.loading-state {
  text-align: center;
  padding: 24px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 24px;
  color: #6b7280;
  background-color: #f9fafb;
  border-radius: 6px;
}

.integrations-list {
  .integration-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .integration-info {
    .integration-type {
      font-weight: 500;
      color: #374151;
      text-transform: capitalize;
      margin-bottom: 4px;
    }

    .integration-email {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .integration-url {
      color: #6b7280;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .integration-date {
      color: #9ca3af;
      font-size: 12px;
    }
  }

  .remove-button {
    padding: 6px 12px;
    background-color: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;

    &:hover:not(:disabled) {
      background-color: #fecaca;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirmation-modal {
  background-color: white;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 18px;
    color: #374151;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;

    &:hover {
      color: #374151;
    }
  }
}

.modal-body {
  padding: 24px;

  .warning-text {
    color: #b91c1c;
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
  }

  .confirmation-input {
    margin-top: 24px;

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #374151;
    }

    input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #e94560;
        box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
      }

      &.error {
        border-color: #ef4444;
        background-color: #fee2e2;
      }
    }
  }

  .input-error {
    color: #ef4444;
    font-size: 14px;
    margin-top: 8px;
  }
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }

  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .confirm-button {
    background-color: #ef4444;
    color: white;
    border: none;

    &:hover:not(:disabled) {
      background-color: #dc2626;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
