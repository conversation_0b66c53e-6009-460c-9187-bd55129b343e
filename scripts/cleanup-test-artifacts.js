#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to clean up old test artifacts from the local test-results directory
 * This script will remove test artifacts older than a specified number of days
 * to prevent disk space issues while preserving recent artifacts for upload.
 */

const fs = require('fs');
const path = require('path');

const TEST_RESULTS_DIR = './test-results';
const DEFAULT_RETENTION_DAYS = 7; // Keep artifacts for 7 days by default

/**
 * Get the age of a file/directory in days
 * @param {string} filePath - Path to the file/directory
 * @returns {number} Age in days
 */
function getAgeInDays(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const now = new Date();
    const ageMs = now.getTime() - stats.mtime.getTime();
    return ageMs / (1000 * 60 * 60 * 24); // Convert to days
  } catch (error) {
    console.error(`Error getting age for ${filePath}:`, error.message);
    return 0;
  }
}

/**
 * Recursively delete a directory and its contents
 * @param {string} dirPath - Path to the directory
 */
function deleteDirectory(dirPath) {
  try {
    if (fs.existsSync(dirPath)) {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ Deleted directory: ${dirPath}`);
      return true;
    }
  } catch (error) {
    console.error(`❌ Failed to delete directory ${dirPath}:`, error.message);
    return false;
  }
  return false;
}

/**
 * Clean up old timestamped files within a test case directory
 * @param {string} testCasePath - Path to the test case directory
 * @param {number} retentionDays - Number of days to retain files
 */
function cleanupTimestampedFiles(testCasePath, retentionDays) {
  try {
    const files = fs.readdirSync(testCasePath);
    let cleanedCount = 0;

    // Find timestamped files (video-*.webm, screenshot-*.png)
    const timestampedFiles = files.filter(file =>
      (file.startsWith('video-') && file.endsWith('.webm')) ||
      (file.startsWith('screenshot-') && file.endsWith('.png'))
    );

    for (const file of timestampedFiles) {
      const filePath = path.join(testCasePath, file);
      const age = getAgeInDays(filePath);

      if (age > retentionDays) {
        try {
          fs.unlinkSync(filePath);
          console.log(`    🗑️  Removed old timestamped file: ${file} (${age.toFixed(1)} days old)`);
          cleanedCount++;
        } catch (deleteError) {
          console.error(`    ❌ Failed to delete ${file}:`, deleteError.message);
        }
      }
    }

    if (cleanedCount > 0) {
      console.log(`    ✅ Cleaned ${cleanedCount} old timestamped files`);
    }

    return cleanedCount;
  } catch (error) {
    console.error(`Error cleaning timestamped files in ${testCasePath}:`, error.message);
    return 0;
  }
}

/**
 * Clean up old test artifacts
 * @param {number} retentionDays - Number of days to retain artifacts
 */
function cleanupTestArtifacts(retentionDays = DEFAULT_RETENTION_DAYS) {
  console.log(`🧹 Starting cleanup of test artifacts older than ${retentionDays} days...`);
  
  if (!fs.existsSync(TEST_RESULTS_DIR)) {
    console.log(`📁 Test results directory does not exist: ${TEST_RESULTS_DIR}`);
    return;
  }

  let totalDeleted = 0;
  let totalSize = 0;

  try {
    // Get all project directories
    const projectDirs = fs.readdirSync(TEST_RESULTS_DIR, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory() && !dirent.name.startsWith('.'))
      .map(dirent => dirent.name);

    console.log(`📊 Found ${projectDirs.length} project directories`);

    for (const projectId of projectDirs) {
      const projectPath = path.join(TEST_RESULTS_DIR, projectId);
      console.log(`\n🔍 Checking project: ${projectId}`);

      // Get all test run directories
      const testRunDirs = fs.readdirSync(projectPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);

      for (const testRunId of testRunDirs) {
        const testRunPath = path.join(projectPath, testRunId);
        const age = getAgeInDays(testRunPath);

        if (age > retentionDays) {
          console.log(`🗑️  Test run ${testRunId} is ${age.toFixed(1)} days old - marking for deletion`);
          
          // Calculate directory size before deletion
          try {
            const stats = fs.statSync(testRunPath);
            if (stats.isDirectory()) {
              // Get approximate size by checking all files recursively
              function calculateDirSize(dirPath) {
                let size = 0;
                try {
                  const items = fs.readdirSync(dirPath);
                  for (const item of items) {
                    const itemPath = path.join(dirPath, item);
                    const itemStats = fs.statSync(itemPath);
                    if (itemStats.isFile()) {
                      size += itemStats.size;
                    } else if (itemStats.isDirectory()) {
                      size += calculateDirSize(itemPath);
                    }
                  }
                } catch (error) {
                  // Ignore errors for individual items
                }
                return size;
              }
              totalSize += calculateDirSize(testRunPath);
            }
          } catch (sizeError) {
            // Ignore size calculation errors
          }

          if (deleteDirectory(testRunPath)) {
            totalDeleted++;
          }
        } else {
          console.log(`✅ Test run ${testRunId} is ${age.toFixed(1)} days old - keeping`);

          // Clean up old timestamped files within test case directories
          try {
            const testCaseDirs = fs.readdirSync(testRunPath, { withFileTypes: true })
              .filter(dirent => dirent.isDirectory())
              .map(dirent => dirent.name);

            for (const testCaseId of testCaseDirs) {
              const testCasePath = path.join(testRunPath, testCaseId);
              console.log(`  🔍 Cleaning timestamped files in test case: ${testCaseId}`);
              cleanupTimestampedFiles(testCasePath, Math.min(retentionDays, 3)); // Keep timestamped files for max 3 days
            }
          } catch (testCaseError) {
            console.error(`  ❌ Error cleaning test case directories in ${testRunId}:`, testCaseError.message);
          }
        }
      }

      // Clean up empty project directories
      try {
        const remainingItems = fs.readdirSync(projectPath);
        if (remainingItems.length === 0) {
          deleteDirectory(projectPath);
          console.log(`🗂️  Removed empty project directory: ${projectId}`);
        }
      } catch (error) {
        // Ignore errors when checking empty directories
      }
    }

    // Clean up Playwright artifacts directories
    console.log(`\n🎭 Cleaning up Playwright artifacts...`);
    const playwrightArtifacts = fs.readdirSync(TEST_RESULTS_DIR, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory() && dirent.name.startsWith('.playwright-artifacts'))
      .map(dirent => dirent.name);

    for (const artifactDir of playwrightArtifacts) {
      const artifactPath = path.join(TEST_RESULTS_DIR, artifactDir);
      const age = getAgeInDays(artifactPath);

      if (age > 1) { // Clean up Playwright artifacts older than 1 day
        console.log(`🗑️  Playwright artifacts ${artifactDir} is ${age.toFixed(1)} days old - deleting`);
        if (deleteDirectory(artifactPath)) {
          totalDeleted++;
        }
      }
    }

  } catch (error) {
    console.error(`❌ Error during cleanup:`, error.message);
  }

  console.log(`\n🎉 Cleanup completed!`);
  console.log(`📊 Statistics:`);
  console.log(`   - Directories deleted: ${totalDeleted}`);
  console.log(`   - Approximate space freed: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`   - Retention period: ${retentionDays} days`);
}

// Parse command line arguments
const args = process.argv.slice(2);
const retentionDays = args.length > 0 ? parseInt(args[0]) : DEFAULT_RETENTION_DAYS;

if (isNaN(retentionDays) || retentionDays < 1) {
  console.error('❌ Invalid retention days. Please provide a positive number.');
  console.log('Usage: node cleanup-test-artifacts.js [retention_days]');
  console.log('Example: node cleanup-test-artifacts.js 7');
  process.exit(1);
}

// Run the cleanup
if (require.main === module) {
  cleanupTestArtifacts(retentionDays);
}

module.exports = { cleanupTestArtifacts };
