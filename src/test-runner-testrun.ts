import WebSocket from 'ws';
import { spawn } from 'child_process';
import axios from 'axios';
import path from 'path';

interface TestExecutionRequest {
  type: string;
  token: string;
  authToken?: string;
  testCaseId: string;
  tcId: string;
  projectId: string;
  testRunId: string;
  steps: any[];
  testCase: {
    title: string;
    precondition: string;
    expectation: string;
    projectId: string;
  };
}



class TestRunnerWebSocketServer {
  private wss: WebSocket.Server;
  private port: number;
  private backendUrl: string;

  constructor(port: number = 3009) {
    this.port = port;
    this.backendUrl = process.env.BACKEND_URL || 'http://localhost:3010';
    
    this.wss = new WebSocket.Server({ 
      port: this.port,
      perMessageDeflate: false
    });
    
    console.log(`TestRun WebSocket server running on port ${this.port}`);
    this.setupWebSocketHandlers();
  }

  private setupWebSocketHandlers() {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('Client connected to TestRun server');
      let isAuthenticated = false;

      ws.on('message', async (message: string) => {
        try {
          const data = JSON.parse(message);
          console.log('Received message type:', data.type);

          switch (data.type) {
            case 'authenticate':
              console.log('Validating JWT token:', data.authToken?.substring(0, 8) + '...');

              try {
                // Use the JWT token (authToken) for authentication, not the AgentQ API key
                const authToken = data.authToken || data.token;

                if (!authToken) {
                  throw new Error('No authentication token provided');
                }

                // Validate JWT token with backend by trying to access a protected endpoint
                const response = await axios.get(`${this.backendUrl}/api-keys`, {
                  headers: {
                    'Authorization': `Bearer ${authToken}`
                  }
                });

                console.log('JWT validation response status:', response.status);

                if (response.status === 200) {
                  isAuthenticated = true;
                  ws.send(JSON.stringify({
                    type: 'auth_success',
                    message: 'Authentication successful'
                  }));
                  console.log('Authentication successful for JWT token:', authToken?.substring(0, 8) + '..., waiting for commands...');
                } else {
                  throw new Error('Invalid JWT token');
                }
              } catch (error: any) {
                console.log('Authentication failed:', error.response?.data?.message || error.message);
                ws.send(JSON.stringify({
                  type: 'auth_failed',
                  message: error.response?.data?.message || error.message || 'Authentication failed'
                }));
              }
              break;

            case 'execute_test':
              if (!isAuthenticated) {
                ws.send(JSON.stringify({
                  type: 'test_error',
                  message: 'Not authenticated'
                }));
                return;
              }

              console.log('Test data for:', data.testCase?.title);
              await this.executeTest(ws, data);
              break;

            default:
              console.log('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error processing message:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      ws.on('close', () => {
        console.log('Client disconnected from TestRun server');
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
      });
    });
  }

  private async executeTest(ws: WebSocket, testData: TestExecutionRequest) {
    const { testCaseId, tcId, projectId, testRunId, steps, testCase, authToken } = testData;

    try {
      // Fetch AgentQ API key from backend using JWT token
      let agentqApiKey = '';
      try {
        const apiKeysResponse = await axios.get(`${this.backendUrl}/api-keys`, {
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        if (apiKeysResponse.data && apiKeysResponse.data.length > 0) {
          agentqApiKey = apiKeysResponse.data[0].apiKey;
          console.log('Retrieved AgentQ API key for test execution');
        }
      } catch (error) {
        console.warn('Failed to fetch AgentQ API key, test may fail:', error);
      }

      // Create test data for master.spec.ts format
      const testDataContent = {
        testCase: {
          id: testCaseId,
          tcId: tcId.toString(),
          title: testCase.title,
          precondition: testCase.precondition,
          expectation: testCase.expectation
        },
        projectId,
        testRunId,
        steps: steps || [],
        authToken: authToken || null
      };

      // Use testrun-detail.spec.ts which has backend integration for TestRunDetail
      const testFilePath = path.join(__dirname, '../tests/testrun-detail.spec.ts');
      console.log('Executing: npx playwright test "' + testFilePath + '" --reporter=line');

      // Execute the test
      const testProcess = spawn('npx', [
        'playwright',
        'test',
        testFilePath,
        '--reporter=line'
      ], {
        cwd: path.join(__dirname, '..'), // Run from the root directory
        stdio: ['pipe', 'pipe', 'pipe'],
        env: {
          ...process.env,
          TEST_DATA: JSON.stringify(testDataContent), // Pass test data as JSON string
          BACKEND_URL: this.backendUrl,
          PROJECT_ID: projectId,
          TEST_RUN_ID: testRunId,
          TEST_CASE_ID: testCaseId,
          AUTH_TOKEN: authToken || '',
          AGENTQ_TOKEN: agentqApiKey, // AgentQ library looks for this variable
          AGENTQ_API_KEY: agentqApiKey, // Alternative variable name
          AGENTQ_JWT_TOKEN: authToken // Pass JWT token for AgentQ library authentication
        }
      });

      ws.send(JSON.stringify({
        type: 'test_start',
        message: 'Test execution started'
      }));

      console.log('Connection stabilized');

      // Handle test output
      testProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log('Test output:', output);
        ws.send(JSON.stringify({
          type: 'test_output',
          output: output
        }));
      });

      testProcess.stderr.on('data', (data) => {
        const error = data.toString();
        console.log('Test error:', error);
        ws.send(JSON.stringify({
          type: 'test_output',
          output: error
        }));
      });

      // Handle test completion
      testProcess.on('close', async (code) => {
        console.log('Test process exited with code:', code);

        const status = code === 0 ? 'passed' : 'failed';

        ws.send(JSON.stringify({
          type: 'test_complete',
          status: status,
          message: `Test ${status} with exit code ${code}`
        }));

        // Upload video and screenshot if test completed (regardless of pass/fail)
        if (authToken) {
          try {
            await this.uploadTestVideo(testData, authToken);
          } catch (videoError) {
            console.error('Failed to upload test video:', videoError);
          }

          try {
            await this.uploadTestScreenshot(testData, authToken);
          } catch (screenshotError) {
            console.error('Failed to upload test screenshot:', screenshotError);
          }
        } else {
          console.log('No auth token available for video/screenshot upload');
        }

        // No cleanup needed since we're using environment variables
      });

      testProcess.on('error', (error) => {
        console.error('Test process error:', error);
        ws.send(JSON.stringify({
          type: 'test_error',
          message: error.message
        }));
      });

    } catch (error: any) {
      console.error('Error executing test:', error);
      ws.send(JSON.stringify({
        type: 'test_error',
        message: error.message || 'Test execution failed'
      }));
    }
  }

  // Upload test video to backend
  private async uploadTestVideo(testData: TestExecutionRequest, authToken: string): Promise<void> {
    let tcId = 'unknown';
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for video files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping video upload');
        return;
      }

      // Find video files - look for patterns like {projectId}/{testRunId}/{tcId}/video.webm
      const projectId = testData.projectId || 'unknown-project';
      const testRunId = testData.testRunId || 'unknown-testrun';
      const tcId = testData.tcId || 'unknown-tc';

      // Try multiple possible video paths
      const possibleVideoPaths = [
        path.join(testResultsDir, projectId, testRunId, tcId.toString(), 'video.webm'),
        path.join(testResultsDir, projectId, testRunId, testData.testCaseId, 'video.webm'),
        path.join(testResultsDir, `testrun-detail-${tcId}`, 'video.webm') // Fallback to old format
      ];

      // Also search for any video.webm files in subdirectories
      const findVideoFiles = (dir: string): string[] => {
        const videoFiles: string[] = [];
        try {
          const items = fs.readdirSync(dir);
          for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              videoFiles.push(...findVideoFiles(fullPath));
            } else if (item === 'video.webm') {
              videoFiles.push(fullPath);
            }
          }
        } catch (error) {
          console.error('Error reading directory:', error);
        }
        return videoFiles;
      };

      // Get all video files
      const allVideoFiles = findVideoFiles(testResultsDir);

      // Add the specific paths we're looking for
      const videoFiles = [...possibleVideoPaths.filter(p => fs.existsSync(p)), ...allVideoFiles];

      if (videoFiles.length === 0) {
        console.log('No video files found for upload');
        return;
      }

      // Use the most recent video file
      const videoFile = videoFiles.sort((a, b) => {
        const statA = fs.statSync(a);
        const statB = fs.statSync(b);
        return statB.mtime.getTime() - statA.mtime.getTime();
      })[0];

      console.log(`Found video file for upload: ${videoFile}`);

      // Wait a moment for the database transaction to complete
      console.log('Waiting for database transaction to complete...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay

      // Get the test result by testCaseId to find the result ID
      console.log(`Fetching test results from: ${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results`);
      const testResultResponse = await axios.get(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results?page=1&limit=100`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 5000
        }
      );
      console.log(`Test results response:`, testResultResponse.data);

      // Handle paginated response structure
      const testResults = testResultResponse.data.results || testResultResponse.data;

      if (!testResults || testResults.length === 0) {
        console.log('No test result found for video upload');
        return;
      }

      // Get the most recent test result for this test case (where isLatest = true)
      const testResult = testResults.find((result: any) =>
        result.testCaseId === testData.testCaseId && result.isLatest === true
      );
      if (!testResult) {
        console.log('No matching latest test result found for video upload');
        console.log('Available test results:', testResults.map((r: any) => ({
          id: r.id,
          testCaseId: r.testCaseId,
          isLatest: r.isLatest,
          status: r.status
        })));
        return;
      }

      const testResultId = testResult.id;
      console.log(`Uploading video for test result ID: ${testResultId}`);

      // Read the video file
      const videoBuffer = fs.readFileSync(videoFile);

      // Create form data for video upload
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: 'video.webm',
        contentType: 'video/webm'
      });

      // Upload video to backend
      await axios.post(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/video`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 30000, // 30 second timeout for video upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log(`Video uploaded successfully for test result: ${testResultId}`);

      // Clean up the video file after successful upload
      try {
        fs.unlinkSync(videoFile);
        console.log(`Cleaned up video file: ${videoFile}`);
      } catch (cleanupError) {
        console.error('Failed to clean up video file:', cleanupError);
      }

    } catch (error) {
      console.error('Failed to upload test video:', error);

      // Log more detailed error information
      if (axios.isAxiosError(error) && error.response) {
        console.error('Video upload response status:', error.response.status);
        console.error('Video upload response data:', error.response.data);
        console.error('Video upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${tcId}/video`);

        // If it's a 500 error on the test results fetch, it might be an endpoint issue
        if (error.config?.url?.includes('/test-results') && !error.config?.url?.includes('/video')) {
          console.error('❌ Failed to fetch test results - the backend endpoint might not exist');
          console.error('❌ This means the video upload endpoint for formal test results is not implemented in the backend');
        }
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('No response received for video upload');
        console.error('Video upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${tcId}/video`);
      } else {
        console.error('Video upload error:', (error as Error).message);
      }
    }
  }

  // Upload test screenshot to backend
  private async uploadTestScreenshot(testData: TestExecutionRequest, authToken: string): Promise<void> {
    let tcId = 'unknown';
    try {
      const fs = require('fs');
      const path = require('path');
      const FormData = require('form-data');

      // Look for screenshot files in test-results directory
      const testResultsDir = path.join(process.cwd(), 'test-results');

      if (!fs.existsSync(testResultsDir)) {
        console.log('No test-results directory found, skipping screenshot upload');
        return;
      }

      // Find screenshot file based on the directory structure: {projectId}/{testRunId}/{tcId}/screenshot.png
      const projectId = testData.projectId;
      const testRunId = testData.testRunId;
      tcId = (testData as any).testCase?.tcId?.toString() || (testData as any).tcId?.toString() || 'unknown';

      const screenshotPath = path.join(testResultsDir, projectId, testRunId, tcId, 'screenshot.png');
      console.log('Looking for screenshot at:', screenshotPath);

      // If custom screenshot doesn't exist, look for Playwright's default screenshot
      if (!fs.existsSync(screenshotPath)) {
        console.log('Custom screenshot not found, looking for Playwright default screenshot...');

        // Look for Playwright's default screenshot pattern
        const playwrightScreenshotPattern = path.join(testResultsDir, 'testrun-detail-*', 'test-failed-*.png');
        const glob = require('glob');
        const playwrightScreenshots = glob.sync(playwrightScreenshotPattern);

        if (playwrightScreenshots.length > 0) {
          const latestScreenshot = playwrightScreenshots[playwrightScreenshots.length - 1];
          console.log('Found Playwright screenshot:', latestScreenshot);

          // Create directory structure for our expected path
          const screenshotDir = path.dirname(screenshotPath);
          if (!fs.existsSync(screenshotDir)) {
            fs.mkdirSync(screenshotDir, { recursive: true });
          }

          // Copy Playwright screenshot to our expected location
          fs.copyFileSync(latestScreenshot, screenshotPath);
          console.log('Copied Playwright screenshot to:', screenshotPath);
        } else {
          console.log('No Playwright screenshot found either, skipping screenshot upload');
          return;
        }
      }

      console.log('Found screenshot file, proceeding with upload');

      // Wait a moment for the database transaction to complete
      console.log('Waiting for database transaction to complete...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay

      // Get test results to find the correct test result ID
      const axios = require('axios');
      console.log(`Fetching test results from: ${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results`);
      const testResultResponse = await axios.get(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results?page=1&limit=100`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 5000
        }
      );
      console.log(`Test results response:`, testResultResponse.data);

      // Handle paginated response structure
      const testResults = testResultResponse.data.results || testResultResponse.data;

      if (!testResults || testResults.length === 0) {
        console.log('No test result found for screenshot upload');
        return;
      }

      // Get the most recent test result for this test case (where isLatest = true)
      const testResult = testResults.find((result: any) =>
        result.testCaseId === testData.testCaseId && result.isLatest === true
      );
      if (!testResult) {
        console.log('No matching latest test result found for screenshot upload');
        console.log('Available test results:', testResults.map((r: any) => ({
          id: r.id,
          testCaseId: r.testCaseId,
          isLatest: r.isLatest,
          status: r.status
        })));
        return;
      }

      const testResultId = testResult.id;
      console.log(`Uploading screenshot for test result ID: ${testResultId}`);
      console.log(`Test result details:`, {
        id: testResult.id,
        testCaseId: testResult.testCaseId,
        status: testResult.status,
        isLatest: testResult.isLatest
      });

      // Read the screenshot file
      const screenshotBuffer = fs.readFileSync(screenshotPath);

      // Create form data for screenshot upload
      const formData = new FormData();
      formData.append('file', screenshotBuffer, {
        filename: 'screenshot.png',
        contentType: 'image/png'
      });

      // Upload screenshot to backend
      await axios.post(
        `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${testResultId}/screenshot`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': `Bearer ${authToken}`
          },
          timeout: 30000, // 30 second timeout for screenshot upload
          maxContentLength: 100 * 1024 * 1024, // 100MB max
          maxBodyLength: 100 * 1024 * 1024
        }
      );

      console.log('Screenshot uploaded successfully');

    } catch (error: any) {
      if (axios.isAxiosError(error) && error.response) {
        console.error('Screenshot upload failed with status:', error.response.status);
        console.error('Screenshot upload error response:', error.response.data);
      } else if (axios.isAxiosError(error) && error.request) {
        console.error('No response received for screenshot upload');
        console.error('Screenshot upload URL was:', `${this.backendUrl}/projects/${testData.projectId}/test-runs/${testData.testRunId}/test-results/${tcId}/screenshot`);
      } else {
        console.error('Screenshot upload error:', (error as Error).message);
      }
    }
  }

  public close() {
    this.wss.close();
  }
}

// Start the server
const server = new TestRunnerWebSocketServer(3009);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('Shutting down TestRun WebSocket server...');
  server.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('Shutting down TestRun WebSocket server...');
  server.close();
  process.exit(0);
});

export default TestRunnerWebSocketServer;
