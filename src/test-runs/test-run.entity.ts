import { <PERSON><PERSON><PERSON>, <PERSON>umn, PrimaryGenerated<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Project } from '../projects/project.entity';

export enum TestCaseSelectionType {
  ALL = 'all',
  SPECIFIC = 'specific',
  DYNAMIC = 'dynamic'
}

@Entity('test_runs')
export class TestRun {
  @ApiProperty({
    description: 'The unique identifier of the test run',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({
    description: 'Test run name',
    example: 'Sprint 1 Regression'
  })
  @Column()
  name: string;

  @ApiProperty({
    description: 'Test run description',
    example: 'Regression test suite for Sprint 1 features'
  })
  @Column('text', { nullable: true })
  description: string;

  @ApiProperty({
    description: 'Test case selection type',
    enum: TestCaseSelectionType,
    example: TestCaseSelectionType.ALL
  })
  @Column({
    type: 'enum',
    enum: TestCaseSelectionType,
    default: TestCaseSelectionType.ALL
  })
  selectionType: TestCaseSelectionType;

  @ApiProperty({
    description: 'Selected test case IDs',
    type: [String],
    example: ['123e4567-e89b-12d3-a456-************']
  })
  @Column('text', { array: true, nullable: true })
  selectedTestCaseIds: string[];

  @ApiProperty({
    description: 'Test run start time',
    example: '2024-03-17T10:00:00Z'
  })
  @Column({ type: 'timestamptz', nullable: true })
  startTime: Date;

  @ApiProperty({
    description: 'Test run end time',
    example: '2024-03-17T11:30:00Z'
  })
  @Column({ type: 'timestamptz', nullable: true })
  endTime: Date;

  @ApiProperty({
    description: 'Test environment',
    example: 'staging'
  })
  @Column({ nullable: true })
  environment: string;

  @ApiProperty({
    description: 'Build version',
    example: 'v1.2.3'
  })
  @Column({ nullable: true })
  build: string;

  @ApiProperty({
    description: 'Release version',
    example: 'R2024.1'
  })
  @Column({ nullable: true })
  release: string;

  @ManyToOne(() => Project, { onDelete: 'CASCADE' })
  project: Project;

  @Column()
  projectId: string;

  @ApiProperty({
    description: 'When the test run was created',
    example: '2024-03-17T09:00:00Z'
  })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({
    description: 'When the test run was last updated',
    example: '2024-03-17T11:30:00Z'
  })
  @UpdateDateColumn()
  updatedAt: Date;
}