import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { AICommand } from '../types';
import fs from 'fs';
import path from 'path';

const userTokenUsage = new Map<string, {
  completion_input: number;
  completion_output: number;
  lastUsed: Date;
}>();

const LLM_PROVIDER = process.env.LLM_PROVIDER?.toUpperCase() || 'GEMINI';

class LLMService {
  async recordTokenUsage(tokenType: 'completion_input' | 'completion_output', tokenUsed: number, apiKey: string): Promise<void> {
    try {
      // Mask the API key for logging
      const maskedToken = apiKey.substring(0, 8) + '...' + apiKey.substring(apiKey.length - 4);
      console.log(`[${new Date().toISOString()}] Recording ${tokenType} usage of ${tokenUsed} tokens for API key: ${maskedToken}`);
      
      // Update the in-memory token usage tracking
      if (userTokenUsage.has(apiKey)) {
        const usage = userTokenUsage.get(apiKey)!;
        usage[tokenType] += tokenUsed;
        usage.lastUsed = new Date();
        userTokenUsage.set(apiKey, usage);
      }
      
      // Call the existing API endpoint
      const response = await fetch(`${process.env.CORE_SERVICE_URL}/profile/usage-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*'
        },
        body: JSON.stringify({
          tokenType,
          tokenUsed,
          token: apiKey
        }),
      });
  
      if (!response.ok) {
        console.error(`[${new Date().toISOString()}] Failed to record token usage: ${response.status} ${response.statusText}`);
      } else {
        console.log(`[${new Date().toISOString()}] Successfully recorded ${tokenUsed} ${tokenType} tokens for API key: ${maskedToken}`);
      }
      
      // Optional: Log to file for persistent records
      this.logToFile({
        timestamp: new Date().toISOString(),
        apiKey: maskedToken,
        tokenType,
        tokenUsed,
        success: response.ok
      });
      
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error recording token usage:`, error);
    }
  }
  
  // Add a method to log token usage to a file
  private logToFile(data: any): void {
    try {
      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }
      
      const today = new Date().toISOString().split('T')[0];
      const logFile = path.join(logsDir, `token-usage-${today}.log`);
      
      // fs.appendFileSync(logFile, JSON.stringify(data) + '\n');
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error writing to log file:`, error);
    }
  }

  async getCommandFromOpenAI(userPrompt: string, pageSource: string, apiKey: string): Promise<AICommand | null> {
    try {
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      const response = await openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || "gpt-4o-mini",
        messages: [
          { role: "system",
            content: `You are a JSON formatter that understands web interactions. Based on the user's prompt and the provided HTML, identify the most likely next action. Return a JSON object strictly in the following format without any explanations or extra text:

            \`\`\`json
            {
              "action": "click" | "fill" | "visit" | "select" | "check" | "uncheck",
              "target": "<CSS selector or XPath or ID that uniquely identifies the target element in the HTML>",
              "value": "<text to fill if action is 'fill', or the value/label/index to select if action is 'select', or the URL if action is 'visit'. Omit if not applicable.>"
            }
            \`\`\`

            Ensure the 'target' is a precise selector that exists in the provided HTML. If the prompt indicates navigation to a new page, use the 'visit' action and provide the URL in the 'value' field.`
          },
          { role: "user",
            content: `Analyze this prompt: "${userPrompt}" along with the HTML source provided. Respond strictly in the JSON format specified below without any additional text.

            \`\`\`json
            {
              "action": "click" | "fill" | "visit" | "select" | "check" | "uncheck",
              "target": "<CSS selector or XPath or ID>",
              "value": "<text for 'fill', value/label/index for 'select', URL for 'visit'>"
            }
            \`\`\`

            HTML:\n${pageSource}`
          }
        ]
      });

      if (!response.choices[0].message.content) {
        throw new Error("Invalid response from OpenAI API");
      }

      // Record token usage
      if (response.usage) {
        await this.recordTokenUsage('completion_input', response.usage.prompt_tokens, apiKey);
        await this.recordTokenUsage('completion_output', response.usage.completion_tokens, apiKey);
      }

      const command = JSON.parse(response.choices[0].message.content.trim());

      if (
        command.action &&
        (
          (command.action === "visit" && typeof command.value === 'string') ||
          (command.target && (
            command.action === "click" ||
            (command.action === "fill" && typeof command.value === 'string') ||
            (command.action === "select" && command.value !== undefined) ||
            command.action === "check" ||
            command.action === "uncheck"
          ))
        )
      ) {
        return command;
      }

      throw new Error("Invalid command structure");
    } catch (error) {
      console.error("Error in getCommandFromOpenAI:", error);
      return null;
    }
  }

  async getCommandFromGemini(userPrompt: string, pageSource: string, apiKey: string): Promise<AICommand | null> {
    let text: string = '';
    let jsonMatch: RegExpMatchArray | null = null;

    try {
      const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');
      const model = genAI.getGenerativeModel({ model: process.env.GEMINI_MODEL || 'gemini-pro' });

      const systemPrompt = `You are a JSON formatter that understands web interactions. Based on the user's prompt and the provided HTML, identify the most likely next action. Return a JSON object strictly in the following format without any explanations or extra text:

      \`\`\`json
      {
        "action": "click" | "fill" | "visit" | "select" | "check" | "uncheck",
        "target": "<CSS selector or XPath or ID that uniquely identifies the target element in the HTML>",
        "value": "<text to fill if action is 'fill', or the value/label/index to select if action is 'select', or the URL if action is 'visit'. Omit if not applicable.>"
      }
      \`\`\`

      Ensure the 'target' is a precise selector that exists in the provided HTML. If the prompt indicates navigation to a new page, use the 'visit' action and provide the URL in the 'value' field.`;

      const prompt = `${systemPrompt}\n\nAnalyze this prompt: "${userPrompt}" along with the HTML source provided. Respond strictly in the JSON format specified below without any additional text.\n\n\`\`\`json\n{\n  "action": "click" | "fill" | "visit" | "select" | "check" | "uncheck",\n  "target": "<CSS selector or XPath or ID>",\n  "value": "<text for 'fill', value/label/index for 'select', URL for 'visit'>"\n}\n\`\`\`\n\nHTML:\n${pageSource}`;

      const result = await model.generateContent(prompt);

      const response = result.response;
      text = response.text();

      if (!text) {
        throw new Error("Invalid response from Gemini API");
      }

      // Estimate token usage for Gemini
      // Note: Gemini doesn't provide token counts directly like OpenAI
      // We're using a rough estimation method here (4 chars ≈ 1 token)
      const inputTokens = Math.ceil(prompt.length / 4);
      const outputTokens = Math.ceil(text.length / 4);
      
      await this.recordTokenUsage('completion_input', inputTokens, apiKey);
      await this.recordTokenUsage('completion_output', outputTokens, apiKey);

      jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("Unable to extract JSON from Gemini response");
      }

      const command = JSON.parse(jsonMatch[0].trim());

      if (
        command.action &&
        (
          (command.action === "visit" && typeof command.value === 'string') ||
          (command.target && (
            command.action === "click" ||
            (command.action === "fill" && typeof command.value === 'string') ||
            (command.action === "select" && command.value !== undefined) ||
            command.action === "check" ||
            command.action === "uncheck"
          ))
        )
      ) {
        return command;
      }

      throw new Error("Invalid command structure");
    } catch (error) {
      console.error("Error in getCommandFromGemini:", error);
      console.error("User prompt:", userPrompt);
      console.error("Response text:", text);
      if (jsonMatch) {
        console.error("Parsed JSON:", jsonMatch[0]);
      }
      return null;
    }
  }

  async getCommandFromAI(userPrompt: string, pageSource: string, apiKey: string): Promise<AICommand | null> {
    switch (LLM_PROVIDER) {
      case 'GEMINI':
        return this.getCommandFromGemini(userPrompt, pageSource, apiKey);
      case 'OPENAI':
      default:
        return this.getCommandFromOpenAI(userPrompt, pageSource, apiKey);
    }
  }
}

export const llmService = new LLMService();
