import { IsNotEmpty, IsString, IsOptional, IsEnum, IsNumber, IsUUID, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TestResultStatus } from '../test-result.entity';

export class CreateTestResultDto {
  @ApiProperty({
    description: 'Test case ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  testCaseId: string;

  @ApiProperty({
    description: 'Test result status',
    enum: TestResultStatus,
    example: TestResultStatus.PASSED
  })
  @IsEnum(TestResultStatus)
  @IsNotEmpty()
  status: TestResultStatus;

  @ApiPropertyOptional({
    description: 'Actual test result',
    example: 'User was successfully redirected to dashboard'
  })
  @IsString()
  @IsOptional()
  actualResult?: string;

  @ApiPropertyOptional({
    description: 'Test execution duration in milliseconds',
    example: 15000
  })
  @IsNumber()
  @IsOptional()
  duration?: number;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Test failed due to network timeout'
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Screenshot URL',
    example: 'https://storage.example.com/screenshots/test-123.png'
  })
  @IsString()
  @IsOptional()
  screenshotUrl?: string;

  @ApiPropertyOptional({
    description: 'Video recording URL',
    example: 'https://storage.example.com/videos/test-123.mp4'
  })
  @IsString()
  @IsOptional()
  videoUrl?: string;

  @ApiPropertyOptional({
    description: 'Google Cloud Storage URL for full logs',
    example: 'gs://agentq-test-logs/test-123/logs.json'
  })
  @IsString()
  @IsOptional()
  logsUrl?: string;

  @ApiPropertyOptional({
    description: 'Array of log messages for test execution',
    example: ['🚀 Starting test execution...', '✅ Test completed successfully']
  })
  @IsArray()
  @IsOptional()
  logs?: string[];
}