import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/user.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('JWT_SECRET'),
    });
    console.log('JWT Strategy initialized with secret:', configService.get('JWT_SECRET').substring(0, 3) + '...');
  }

  async validate(payload: any) {
    try {
      console.log('Validating JWT payload:', JSON.stringify(payload));
      
      // Check if the token is from the login service or our own service
      const user = await this.usersService.findOne(payload.email);
      
      if (!user) {
        // If user not found, check if we have enough information to create one
        // This handles tokens from both our own system and external systems
        if (payload.email && payload.sub) {
          console.log('Creating user from JWT payload');
          return this.usersService.createFromExternalAuth({
            id: payload.sub,
            email: payload.email,
            name: payload.name || payload.email.split('@')[0],
            role: payload.role || 'user',
            companyId: payload.companyId
          });
        }
        
        console.log('User not found and not enough information to create one');
        return null;
      }
      
      console.log('User found:', user.email);
      return user;
    } catch (error) {
      console.error('JWT validation error:', error);
      throw new UnauthorizedException('Invalid token');
    }
  }
}
