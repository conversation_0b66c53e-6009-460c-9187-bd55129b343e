interface TestStep {
  step: number;
  stepName: string;
  action?: string;
  target?: string;
  value?: string;
  prompt?: string;
}

interface TestCase {
  title: string;
  precondition?: string;
  expectation?: string;
}

interface TestExecutionRequest {
  testCaseId: string;
  tcId: string;
  steps: TestStep[];
  testCase: TestCase;
}

interface TestWebSocketResponse {
  type: 'test_start' | 'test_output' | 'test_complete' | 'test_error' | 'error' | 'auth_success';
  message?: string;
  testCaseId?: string;
  tcId?: string;
  output?: string;
  isError?: boolean;
  status?: 'passed' | 'failed';
  stdout?: string;
  stderr?: string;
}

export class TestWebSocketClient {
  private ws: WebSocket | null = null;
  private connected: boolean = false;
  private apiKey: string;
  private userJwtToken: string | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 1000;

  // Event handlers
  public onTestStart?: (data: TestWebSocketResponse) => void;
  public onTestOutput?: (data: TestWebSocketResponse) => void;
  public onTestComplete?: (data: TestWebSocketResponse) => void;
  public onTestError?: (data: TestWebSocketResponse) => void;
  public onError?: (error: string) => void;
  public onConnected?: () => void;
  public onDisconnected?: () => void;

  constructor(apiKey: string, wsUrl: string = 'ws://localhost:3008', userJwtToken?: string) {
    this.apiKey = apiKey;
    this.userJwtToken = userJwtToken || null;
    this.connect(wsUrl);
  }

  private connect(wsUrl: string): void {
    try {
      this.ws = new WebSocket(wsUrl);
      this.setupEventHandlers();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.handleReconnect(wsUrl);
    }
  }

  private setupEventHandlers(): void {
    if (!this.ws) return;

    this.ws.onopen = () => {
      console.log('WebSocket connected');
      this.connected = true;
      this.reconnectAttempts = 0;
      
      // Authenticate immediately after connection
      this.authenticate();
      
      if (this.onConnected) {
        this.onConnected();
      }
    };

    this.ws.onmessage = (event) => {
      try {
        const data: TestWebSocketResponse = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected');
      this.connected = false;
      
      if (this.onDisconnected) {
        this.onDisconnected();
      }
      
      // Attempt to reconnect
      this.handleReconnect();
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      if (this.onError) {
        this.onError('WebSocket connection error');
      }
    };
  }

  private handleMessage(data: TestWebSocketResponse): void {
    switch (data.type) {
      case 'auth_success':
        console.log('Authentication successful');
        break;
      
      case 'test_start':
        console.log('Test execution started');
        if (this.onTestStart) {
          this.onTestStart(data);
        }
        break;
      
      case 'test_output':
        if (this.onTestOutput) {
          this.onTestOutput(data);
        }
        break;
      
      case 'test_complete':
        console.log('Test execution completed:', data.status);
        if (this.onTestComplete) {
          this.onTestComplete(data);
        }
        break;
      
      case 'test_error':
      case 'error':
        console.error('Test execution error:', data.message);
        if (this.onTestError) {
          this.onTestError(data);
        }
        break;
      
      default:
        console.log('Unknown message type:', data.type);
    }
  }

  private authenticate(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    this.ws.send(JSON.stringify({
      type: 'auth',
      token: this.apiKey
    }));
  }

  private handleReconnect(wsUrl?: string): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      if (this.onError) {
        this.onError('Failed to reconnect to WebSocket server');
      }
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (wsUrl) {
        this.connect(wsUrl);
      }
    }, delay);
  }

  public executeTest(testData: TestExecutionRequest): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      if (this.onError) {
        this.onError('WebSocket is not connected');
      }
      return;
    }

    this.ws.send(JSON.stringify({
      type: 'execute_test',
      token: this.apiKey,
      userJwtToken: this.userJwtToken, // Send user JWT token for backend authentication
      ...testData
    }));
  }

  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.connected = false;
  }

  public isConnected(): boolean {
    return this.connected && this.ws?.readyState === WebSocket.OPEN;
  }
}

export type { TestExecutionRequest, TestStep, TestCase, TestWebSocketResponse };
