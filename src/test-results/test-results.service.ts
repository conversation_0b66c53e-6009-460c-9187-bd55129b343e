import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestResult } from './test-result.entity';
import { Storage } from '@google-cloud/storage';

@Injectable()
export class TestResultsService {
  private storage: Storage;
  private bucketName: string;

  constructor(
    @InjectRepository(TestResult)
    private testResultsRepository: Repository<TestResult>,
  ) {
    // Initialize Google Cloud Storage
    this.storage = new Storage({
      projectId: process.env.GCP_PROJECT_ID,
      credentials: {
        client_email: process.env.GCP_CLIENT_EMAIL,
        private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      },
    });
    this.bucketName = process.env.GCP_BUCKET_NAME || 'agentq-test-logs';
  }

  // Existing methods...

  /**
   * Upload logs to Google Cloud Storage
   * @param fileName - The file path in GCS
   * @param logs - Array of log messages
   * @returns Promise<string> - The GCS URL
   */
  async uploadLogs(fileName: string, logs: string[]): Promise<string> {
    try {
      const file = this.storage.bucket(this.bucketName).file(fileName);

      const logData = {
        timestamp: new Date().toISOString(),
        logs,
        metadata: {
          totalLogs: logs.length,
          uploadedAt: new Date().toISOString(),
        },
      };

      // Upload the logs as JSON
      await file.save(JSON.stringify(logData, null, 2), {
        metadata: {
          contentType: 'application/json',
        },
      });

      // Make the file publicly readable
      await file.makePublic();

      // Return the public URL
      return `https://storage.googleapis.com/${this.bucketName}/${fileName}`;
    } catch (error) {
      console.error('Error uploading logs to GCS:', error);
      throw error;
    }
  }

  /**
   * Update test result with logs URL
   * @param resultId - The test result ID
   * @param logsUrl - The URL to the logs in GCS
   */
  async updateTestResultLogsUrl(resultId: string, logsUrl: string): Promise<void> {
    await this.testResultsRepository.update(resultId, { logsUrl });
  }
}