<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import Sidebar from '../components/dashboard/Sidebar.vue'
import ApiKeySettings from '../components/dashboard/ApiKeySettings.vue'
import GeneralSettings from '../components/dashboard/GeneralSettings.vue'
import IntegrationsSettings from '../components/dashboard/IntegrationsSettings.vue'

const router = useRouter()
const authStore = useAuthStore()
const userData = ref<{ email: string; name?: string } | null>(null);
const loading = ref(true)
const error = ref('')
const sidebarCollapsed = ref(false)
const activeSetting = ref<'general' | 'api' | 'integrations' | null>(null);

const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}

// Handle the show-settings event
const handleShowSettings = (event: CustomEvent) => {
  const { setting } = event.detail;
  if (setting === 'integrations' || setting === 'api' || setting === 'general') {
    activeSetting.value = setting;
  }
};

onMounted(async () => {
  try {
    loading.value = true
    console.log('Checking authentication...')
    console.log('Current token:', authStore.token)
    userData.value = await authStore.checkAuth()
    console.log('Authentication successful, user data:', userData.value)
    loading.value = false

    // Add event listener for show-settings event
    window.addEventListener('show-settings', handleShowSettings as EventListener);
  } catch (err) {
    loading.value = false
    error.value = 'Failed to load user data. Please log in again.'
    console.error('Authentication error:', err)
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  }
})

// Remove event listener when component is unmounted
onUnmounted(() => {
  window.removeEventListener('show-settings', handleShowSettings as EventListener);
})
</script>

<template>
  <div class="dashboard-container" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
    <!-- Sidebar Component -->
    <Sidebar
      :sidebarCollapsed="sidebarCollapsed"
      :activeSetting="activeSetting"
      @update:sidebarCollapsed="sidebarCollapsed = $event"
      @update:activeSetting="activeSetting = $event"
      @logout="logout"
    />

    <!-- Main content -->
    <main class="main-content">
      <header class="dashboard-header">
        <button class="mobile-menu-button" @click="toggleSidebar">
          <span>☰</span>
        </button>
        <div class="user-welcome" v-if="userData">
          Welcome, {{ userData.name || userData.email }}
        </div>
      </header>

      <div class="dashboard-content">
        <div v-if="loading" class="loading">
          Loading user data...
        </div>

        <div v-else-if="error" class="error-message">
          {{ error }}
        </div>

        <template v-else>
          <!-- API Key Settings Component -->
          <ApiKeySettings
            v-if="activeSetting === 'api'"
          />

          <!-- General Settings Component -->
          <GeneralSettings
            v-else-if="activeSetting === 'general'"
            @update:activeSetting="activeSetting = $event"
          />

          <!-- Integrations Settings Component -->
          <IntegrationsSettings
            v-else-if="activeSetting === 'integrations'"
          />

          <!-- Router View for Main Content -->
          <router-view v-else :userData="userData" />
        </template>
      </div>
    </main>
  </div>
</template>

<style lang="scss" scoped>
.dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f5f7fa;
  position: relative;
  transition: all 0.3s ease;
}

.main-content {
  flex: 1;
  margin-left: 230px;
  transition: all 0.3s ease;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed .main-content {
  margin-left: 70px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.mobile-menu-button {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
}

.user-welcome {
  font-weight: 500;
}

.dashboard-content {
  padding: 5px;
  flex: 1;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6b7280;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

// Responsive styles
@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important;
  }

  .mobile-menu-button {
    display: block;
  }

  .dashboard-container.sidebar-open .main-content {
    opacity: 0.5;
  }
}
</style>