import { Queue, Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import WebSocket from 'ws';
import { TestRunnerService } from './test-runner';

interface TestExecutionJob {
  testCaseId: string;
  tcId: string;
  projectId?: string;
  testRunId?: string;
  steps: any[];
  testCase: {
    title: string;
    precondition?: string;
    expectation?: string;
    projectId?: string;
  };
  apiKey: string;
  authToken?: string;
  userId?: string;
  userEmail?: string;
}

export class QueueService {
  private static instance: QueueService;
  private redis: Redis;
  private testQueue: Queue;
  private worker: Worker;
  private activeJobs: Map<string, Job> = new Map();

  private constructor() {
    // Initialize Redis connection
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB || '0'),
      enableReadyCheck: false,
      maxRetriesPerRequest: null,
    });

    // Initialize the test execution queue
    this.testQueue = new Queue('test-execution', {
      connection: this.redis,
      defaultJobOptions: {
        removeOnComplete: 10, // Keep last 10 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs
        attempts: 1, // Don't retry failed tests automatically
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    });

    // Initialize the worker
    this.worker = new Worker(
      'test-execution',
      async (job: Job<TestExecutionJob>) => {
        return this.processTestJob(job);
      },
      {
        connection: this.redis,
        concurrency: 1, // Only process one test at a time
      }
    );

    this.setupWorkerEventHandlers();
  }

  public static getInstance(): QueueService {
    if (!QueueService.instance) {
      QueueService.instance = new QueueService();
    }
    return QueueService.instance;
  }

  private setupWorkerEventHandlers(): void {
    this.worker.on('completed', (job: Job) => {
      console.log(`✅ Test job ${job.id} completed successfully`);
      this.activeJobs.delete(job.id!);
      this.notifyJobUpdate(job, 'completed');
    });

    this.worker.on('failed', (job: Job | undefined, err: Error) => {
      if (job) {
        console.log(`❌ Test job ${job.id} failed:`, err.message);
        this.activeJobs.delete(job.id!);
        this.notifyJobUpdate(job, 'failed', err.message);
      }
    });

    this.worker.on('active', (job: Job) => {
      console.log(`🚀 Test job ${job.id} started processing`);
      this.activeJobs.set(job.id!, job);
      this.notifyJobUpdate(job, 'active');
    });

    this.worker.on('progress', (job: Job, progress: any) => {
      console.log(`📊 Test job ${job.id} progress:`, progress);
      this.notifyJobUpdate(job, 'progress', undefined, progress);
    });
  }

  private notifyJobUpdate(job: Job, status: string, error?: string, progress?: number | object): void {
    const jobData = job.data as TestExecutionJob;
    // Try to get WebSocket connection from TestRunnerService
    const ws = TestRunnerService.getClient(jobData.apiKey);
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({
        type: 'queue_update',
        jobId: job.id,
        status: status,
        progress: progress,
        error: error,
        queuePosition: job.opts?.priority || 0
      }));
    }
  }

  public async addTestJob(jobData: TestExecutionJob & { ws: WebSocket }): Promise<{ jobId: string; queuePosition: number }> {
    try {
      // Remove ws from job data before adding to queue (since it can't be serialized)
      const { ws, ...queueJobData } = jobData;

      // Add job to queue
      const job = await this.testQueue.add('execute-test', queueJobData, {
        // Use a simple counter for FIFO ordering (lower number = higher priority)
        priority: 1, // All jobs have same priority for FIFO processing
        delay: 0,
      });

      // Get current queue position
      const waiting = await this.testQueue.getWaiting();
      const queuePosition = waiting.length;

      console.log(`📋 Added test job ${job.id} to queue. Position: ${queuePosition}`);

      // Notify user about queue position
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'test_queued',
          jobId: job.id,
          queuePosition: queuePosition,
          message: queuePosition === 0 ? 'Test will start shortly...' : `Test queued. Position: ${queuePosition + 1}`
        }));
      }

      return {
        jobId: job.id!,
        queuePosition: queuePosition
      };
    } catch (error) {
      console.error('Failed to add test job to queue:', error);
      throw error;
    }
  }

  private async processTestJob(job: Job<TestExecutionJob>): Promise<void> {
    const { testCaseId, tcId, projectId, testRunId, steps, testCase, apiKey, authToken } = job.data;

    try {
      // Update job progress
      await job.updateProgress(10);

      // Execute the test using existing TestRunnerService
      if (testRunId) {
        // This is a TestRun execution
        const testRunnerService = new (require('../test-runner-testrun').TestRunnerService)();
        await testRunnerService.executeTest(null, {
          testCaseId,
          tcId,
          projectId: projectId!,
          testRunId,
          steps,
          testCase,
          token: apiKey,
          authToken
        });
      } else {
        // This is a TestAutomation execution
        // The WebSocket connection should already be registered with TestRunnerService
        await TestRunnerService.executeTest(apiKey, {
          testCaseId,
          tcId,
          steps,
          testCase,
          userJwtToken: authToken
        }, true); // Skip connection close since we're managing it in the queue
      }

      await job.updateProgress(100);

    } catch (error) {
      console.error(`Test execution failed for job ${job.id}:`, error);
      throw error;
    }
  }

  public async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
  }> {
    const waiting = await this.testQueue.getWaiting();
    const active = await this.testQueue.getActive();
    const completed = await this.testQueue.getCompleted();
    const failed = await this.testQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  public async getJobStatus(jobId: string): Promise<any> {
    const job = await this.testQueue.getJob(jobId);
    if (!job) {
      return null;
    }

    return {
      id: job.id,
      status: await job.getState(),
      progress: job.progress,
      data: job.data,
      createdAt: job.timestamp,
      processedAt: job.processedOn,
      finishedAt: job.finishedOn,
      failedReason: job.failedReason,
    };
  }

  public async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.testQueue.getJob(jobId);
      if (job) {
        await job.remove();
        this.activeJobs.delete(jobId);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Failed to cancel job ${jobId}:`, error);
      return false;
    }
  }

  public getQueue(): Queue {
    return this.testQueue;
  }

  public async cleanup(): Promise<void> {
    await this.worker.close();
    await this.testQueue.close();
    await this.redis.quit();
  }
}
