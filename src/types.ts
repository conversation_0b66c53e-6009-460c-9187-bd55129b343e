export type WSMessageType = 'auth' | 'command' | 'ping' | 'execute_test';

export interface WSBaseMessage {
  type: WSMessageType;
  token: string;
}

export interface WSAuthMessage extends WSBaseMessage {
  type: 'auth';
}

export interface WSCommandMessage extends WSBaseMessage {
  type: 'command';
  prompt: string;
  pageSource: string;
}

export interface WSPingMessage extends WSBaseMessage {
  type: 'ping';
}

export interface WSExecuteTestMessage extends WSBaseMessage {
  type: 'execute_test';
  testCaseId: string;
  tcId: string;
  steps: Array<{
    step: number;
    stepName: string;
    action?: string;
    target?: string;
    value?: string;
    prompt?: string;
  }>;
  testCase: {
    title: string;
    precondition?: string;
    expectation?: string;
  };
}

export type WSMessage = WSAuthMessage | WSCommandMessage | WSPingMessage | WSExecuteTestMessage;

export interface AICommand {
  action: string;
  target: string;
  value?: string | boolean | number | null;
}

export interface WSResponse {
  type: 'response' | 'error' | 'auth_success' | 'pong' | 'complete' | 'test_start' | 'test_output' | 'test_complete' | 'test_error';
  command?: AICommand;
  message?: string;
  code?: number;
  testCaseId?: string;
  tcId?: string;
  output?: string;
  isError?: boolean;
  status?: 'passed' | 'failed';
  stdout?: string;
  stderr?: string;
}